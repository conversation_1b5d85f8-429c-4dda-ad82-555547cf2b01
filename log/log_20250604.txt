2025-06-04 15:11:28,181  INFO: 测试开始!
2025-06-04 15:11:28,184  INFO: 测试进度：0/1
2025-06-04 15:11:28,184  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:11:28,185  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:11:28,218  INFO: === 初始化测试环境 ===
2025-06-04 15:11:28,219  INFO: 正在初始化设备连接...
2025-06-04 15:11:28,288  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:11:28,297  INFO: 正在创建测试报告...
2025-06-04 15:11:28,298  ERROR: 测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:11:28,298  INFO: === 清理测试环境 ===
2025-06-04 15:11:28,299  INFO: 设备连接已断开
2025-06-04 15:11:28,303  ERROR: 测试终止！测试错误：测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:17:42,243  INFO: 测试开始!
2025-06-04 15:17:42,244  INFO: 测试进度：0/1
2025-06-04 15:17:42,245  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:17:42,245  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:17:42,269  INFO: === 初始化测试环境 ===
2025-06-04 15:17:42,270  INFO: 正在初始化设备连接...
2025-06-04 15:17:42,339  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:17:42,344  INFO: 正在创建测试报告...
2025-06-04 15:17:42,344  ERROR: 测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:17:42,345  INFO: === 清理测试环境 ===
2025-06-04 15:17:42,346  INFO: 设备连接已断开
2025-06-04 15:17:42,354  ERROR: 测试终止！测试错误：测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:19:39,749  INFO: 测试开始!
2025-06-04 15:19:39,752  INFO: 测试进度：0/1
2025-06-04 15:19:39,752  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:19:39,753  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:19:39,765  INFO: === 初始化测试环境 ===
2025-06-04 15:19:39,765  INFO: 正在初始化设备连接...
2025-06-04 15:19:39,835  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:19:39,839  INFO: 正在创建测试报告...
2025-06-04 15:19:39,840  ERROR: 测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:19:39,841  INFO: === 清理测试环境 ===
2025-06-04 15:19:39,842  INFO: 设备连接已断开
2025-06-04 15:19:39,849  ERROR: 测试终止！测试错误：测试执行过程中发生异常: too many values to unpack (expected 3)
2025-06-04 15:22:17,934  INFO: 测试开始!
2025-06-04 15:22:17,939  INFO: 测试进度：0/1
2025-06-04 15:22:17,940  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:22:17,940  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:22:17,987  INFO: === 初始化测试环境 ===
2025-06-04 15:22:17,992  INFO: 正在初始化设备连接...
2025-06-04 15:22:18,059  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:22:18,072  INFO: 正在创建测试报告...
2025-06-04 15:22:18,077  ERROR: 测试终止！测试错误：测试用例执行异常( 测试执行过程中发生异常: too many values to unpack (expected 3) )
2025-06-04 15:25:56,470  INFO: 测试开始!
2025-06-04 15:25:56,473  INFO: 测试进度：0/1
2025-06-04 15:25:56,473  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:25:56,474  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:25:56,497  INFO: === 初始化测试环境 ===
2025-06-04 15:25:56,498  INFO: 正在初始化设备连接...
2025-06-04 15:25:56,568  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:25:56,577  INFO: 正在创建测试报告...
2025-06-04 15:25:56,577  ERROR: 创建报告文件失败: 当前用例不在self.data中
2025-06-04 15:25:56,582  ERROR: 测试终止！测试错误：当前用例不在self.data中
2025-06-04 15:52:31,558  INFO: 测试开始!
2025-06-04 15:52:31,559  INFO: 测试进度：0/1
2025-06-04 15:52:31,560  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 15:52:31,561  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 15:52:31,586  INFO: === 初始化测试环境 ===
2025-06-04 15:52:31,586  INFO: 正在初始化设备连接...
2025-06-04 15:52:31,653  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 15:52:31,662  INFO: 正在创建测试报告...
2025-06-04 15:52:31,663  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 15:52:31,664  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_155231
2025-06-04 15:52:31,665  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_155231/superlink_rx_gauss_report_20250604_155231.xlsx
2025-06-04 15:52:31,667  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_155231/superlink_rx_gauss_report_20250604_155231.xlsx
2025-06-04 15:52:31,669  INFO: === 开始高斯测试 ===
2025-06-04 15:52:31,670  INFO: === 生成高斯测试用例 ===
2025-06-04 15:52:31,670  INFO: 高斯测试参数配置:
2025-06-04 15:52:31,674  INFO:   SYM_RATES: [0]
2025-06-04 15:52:31,675  INFO:   S_CODE_EN: [1]
2025-06-04 15:52:31,675  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-04 15:52:31,676  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-04 15:52:31,676  INFO:   DATA_LEN: [6]
2025-06-04 15:52:31,677  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-04 15:52:31,677  INFO: 成功生成 10 个高斯测试用例
2025-06-04 15:52:31,677  INFO: 总测试用例数: 10
2025-06-04 15:52:31,677  INFO: 
=== 执行测试用例 1/10 ===
2025-06-04 15:52:31,678  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 15:52:31,678  INFO: 开始配置CMW信号发生器...
2025-06-04 15:52:37,687  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 15:52:37,689  INFO: 开始加载ARB文件...
2025-06-04 15:52:37,931  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 15:53:48,331  INFO: 初始化RF板...
2025-06-04 15:53:48,648  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 15:54:43,055  INFO: 测试成功 - PER: 31.842381786339757%, 极性误差: 9.5215e-03~2.6001e-02
2025-06-04 15:54:43,107  INFO: 
=== 执行测试用例 2/10 ===
2025-06-04 15:54:43,107  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 15:54:43,109  INFO: 开始配置CMW信号发生器...
2025-06-04 15:54:49,145  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 15:54:49,146  INFO: 开始加载ARB文件...
2025-06-04 15:54:49,368  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 15:55:59,775  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 15:56:32,863  INFO: 测试成功 - PER: 12.20926486695445%, 极性误差: 1.8311e-03~2.3193e-02
2025-06-04 15:56:32,889  INFO: 
=== 执行测试用例 3/10 ===
2025-06-04 15:56:32,889  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-04 15:56:32,891  INFO: 开始配置CMW信号发生器...
2025-06-04 15:56:38,916  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 15:56:38,917  INFO: 开始加载ARB文件...
2025-06-04 15:56:39,138  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-04 15:57:49,537  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 15:58:22,766  INFO: 测试成功 - PER: 3.3278501834746343%, 极性误差: 4.8828e-04~4.8828e-03
2025-06-04 15:58:22,799  INFO: 
=== 执行测试用例 4/10 ===
2025-06-04 15:58:22,802  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-04 15:58:22,805  INFO: 开始配置CMW信号发生器...
2025-06-04 15:58:28,823  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 15:58:28,824  INFO: 开始加载ARB文件...
2025-06-04 15:58:29,151  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-04 15:59:39,548  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 16:00:12,755  INFO: 测试成功 - PER: 0.46240577690505047%, 极性误差: 0.0000e+00~4.8828e-04
2025-06-04 16:00:12,786  INFO: 
=== 执行测试用例 5/10 ===
2025-06-04 16:00:12,786  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-04 16:00:12,786  INFO: 开始配置CMW信号发生器...
2025-06-04 16:00:18,860  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:00:18,861  INFO: 开始加载ARB文件...
2025-06-04 16:00:19,190  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-04 16:01:29,621  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 16:02:02,999  INFO: 测试成功 - PER: 0.006311537490533148%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-04 16:02:03,038  INFO: 
=== 执行测试用例 6/10 ===
2025-06-04 16:02:03,038  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-04 16:02:03,040  INFO: 开始配置CMW信号发生器...
2025-06-04 16:02:09,077  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:02:09,078  INFO: 开始加载ARB文件...
2025-06-04 16:02:09,308  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-04 16:03:19,789  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 16:03:48,532  INFO: 测试成功 - PER: 0.0%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-04 16:03:48,565  INFO: 
=== 执行测试用例 7/10 ===
2025-06-04 16:03:48,566  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-04 16:03:48,567  INFO: 开始配置CMW信号发生器...
2025-06-04 16:03:54,594  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:03:54,595  INFO: 开始加载ARB文件...
2025-06-04 16:03:54,640  INFO: 
=== 执行测试用例 8/10 ===
2025-06-04 16:03:54,641  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-04 16:03:54,642  INFO: 开始配置CMW信号发生器...
2025-06-04 16:03:55,654  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:03:55,655  INFO: 开始加载ARB文件...
2025-06-04 16:03:55,887  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-04 16:05:06,306  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-04 16:07:18,828  INFO: 测试成功 - PER: 19.94553320129261%, 极性误差: 1.9775e-02~3.0762e-02
2025-06-04 16:07:18,881  INFO: 
=== 执行测试用例 9/10 ===
2025-06-04 16:07:18,883  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-04 16:07:18,883  INFO: 开始配置CMW信号发生器...
2025-06-04 16:07:25,011  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:07:25,012  INFO: 开始加载ARB文件...
2025-06-04 16:07:25,311  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-04 16:08:35,708  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-04 16:09:08,981  INFO: 测试成功 - PER: 4.557316529819866%, 极性误差: 3.0518e-03~5.8594e-03
2025-06-04 16:09:09,027  INFO: 
=== 执行测试用例 10/10 ===
2025-06-04 16:09:09,027  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-04 16:09:09,028  INFO: 开始配置CMW信号发生器...
2025-06-04 16:09:15,055  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:09:15,056  INFO: 开始加载ARB文件...
2025-06-04 16:09:15,276  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-04 16:10:25,692  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-04 16:10:58,936  INFO: 测试成功 - PER: 0.8891146957957563%, 极性误差: 7.3242e-04~3.2959e-03
2025-06-04 16:10:58,978  INFO: === 高斯测试完成 ===
2025-06-04 16:10:58,978  INFO: === 清理测试环境 ===
2025-06-04 16:10:58,981  INFO: 设备连接已断开
2025-06-04 16:10:58,982  INFO: === 测试完成 ===
2025-06-04 16:10:58,982  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-04 16:10:58,982  INFO: 测试进度：1/1
2025-06-04 16:11:03,985  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-04 16:11:03,989  INFO: 测试完成！总共测试耗时：00:17:31
2025-06-04 16:19:58,392  INFO: 测试开始!
2025-06-04 16:19:58,393  INFO: 测试进度：0/1
2025-06-04 16:19:58,394  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 16:19:58,395  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 16:19:58,430  INFO: === 初始化测试环境 ===
2025-06-04 16:19:58,432  INFO: 正在初始化设备连接...
2025-06-04 16:19:58,502  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 16:19:58,514  INFO: 正在创建测试报告...
2025-06-04 16:19:58,515  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 16:19:58,516  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_161958
2025-06-04 16:19:58,517  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_161958/superlink_rx_gauss_report_20250604_161958.xlsx
2025-06-04 16:19:58,521  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_161958/superlink_rx_gauss_report_20250604_161958.xlsx
2025-06-04 16:19:58,522  INFO: === 开始高斯测试 ===
2025-06-04 16:19:58,525  INFO: === 生成高斯测试用例 ===
2025-06-04 16:19:58,525  INFO: 高斯测试参数配置:
2025-06-04 16:19:58,525  INFO:   SYM_RATES: [0]
2025-06-04 16:19:58,525  INFO:   S_CODE_EN: [1]
2025-06-04 16:19:58,526  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-04 16:19:58,526  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-04 16:19:58,526  INFO:   DATA_LEN: [6]
2025-06-04 16:19:58,526  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-04 16:19:58,527  INFO: 成功生成 2 个高斯测试用例
2025-06-04 16:19:58,527  INFO: 总测试用例数: 2
2025-06-04 16:19:58,527  INFO: 
=== 执行测试用例 1/2 ===
2025-06-04 16:19:58,532  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 16:19:58,533  INFO: 开始配置CMW信号发生器...
2025-06-04 16:20:04,547  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:20:04,549  INFO: 开始加载ARB文件...
2025-06-04 16:20:04,769  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 16:21:15,173  INFO: 初始化RF板...
2025-06-04 16:21:15,498  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 16:22:09,730  INFO: 测试成功 - PER: 32.58823114621161%, 极性误差: 8.7891e-03~3.9673e-02
2025-06-04 16:22:09,759  ERROR: 测试用例 1 执行失败: Cannot convert {'rx_ok': 12522, 'tx': 0, 'crc_err': 2470, 'len_err': 0, 'sync_err': 4556} to Excel
2025-06-04 16:22:09,831  INFO: 
=== 执行测试用例 2/2 ===
2025-06-04 16:22:09,831  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 16:22:09,833  INFO: 开始配置CMW信号发生器...
2025-06-04 16:22:15,853  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:22:15,854  INFO: 开始加载ARB文件...
2025-06-04 16:22:16,074  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 16:22:54,542  INFO: 测试停止!
2025-06-04 16:24:32,208  INFO: 测试开始!
2025-06-04 16:24:32,211  INFO: 测试进度：0/1
2025-06-04 16:24:32,212  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-04 16:24:32,212  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-04 16:24:32,240  INFO: === 初始化测试环境 ===
2025-06-04 16:24:32,241  INFO: 正在初始化设备连接...
2025-06-04 16:24:32,311  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-04 16:24:32,319  INFO: 正在创建测试报告...
2025-06-04 16:24:32,320  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告
2025-06-04 16:24:32,320  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_162432
2025-06-04 16:24:32,322  INFO: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_162432/superlink_rx_gauss_report_20250604_162432.xlsx
2025-06-04 16:24:32,323  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250604_162432/superlink_rx_gauss_report_20250604_162432.xlsx
2025-06-04 16:24:32,323  INFO: === 开始高斯测试 ===
2025-06-04 16:24:32,324  INFO: === 生成高斯测试用例 ===
2025-06-04 16:24:32,330  INFO: 高斯测试参数配置:
2025-06-04 16:24:32,330  INFO:   SYM_RATES: [0]
2025-06-04 16:24:32,330  INFO:   S_CODE_EN: [1]
2025-06-04 16:24:32,333  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-04 16:24:32,333  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-04 16:24:32,334  INFO:   DATA_LEN: [6]
2025-06-04 16:24:32,334  INFO: 测试用例数量(36)超过限制(2)，将只执行前2个用例
2025-06-04 16:24:32,334  INFO: 成功生成 2 个高斯测试用例
2025-06-04 16:24:32,334  INFO: 总测试用例数: 2
2025-06-04 16:24:32,334  INFO: 
=== 执行测试用例 1/2 ===
2025-06-04 16:24:32,334  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-04 16:24:32,335  INFO: 开始配置CMW信号发生器...
2025-06-04 16:24:38,468  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:24:38,468  INFO: 开始加载ARB文件...
2025-06-04 16:24:38,696  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-04 16:25:49,096  INFO: 初始化RF板...
2025-06-04 16:25:49,424  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 16:26:43,776  INFO: 测试成功 - PER: 31.40530528081641%, 极性误差: 8.0566e-03~2.6367e-02
2025-06-04 16:26:43,808  ERROR: 测试用例 1 执行失败: Cannot convert {'rx_ok': 12549, 'tx': 0, 'crc_err': 2435, 'len_err': 0, 'sync_err': 4527} to Excel
2025-06-04 16:26:43,884  INFO: 
=== 执行测试用例 2/2 ===
2025-06-04 16:26:43,885  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-04 16:26:43,886  INFO: 开始配置CMW信号发生器...
2025-06-04 16:26:49,949  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-04 16:26:49,950  INFO: 开始加载ARB文件...
2025-06-04 16:26:50,182  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\6byte_10000\R1polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-04 16:28:00,584  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-04 16:28:31,330  INFO: 测试停止!
