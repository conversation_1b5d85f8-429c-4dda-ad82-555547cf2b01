2025-06-12 11:18:13,728  INFO: 测试开始!
2025-06-12 11:18:13,730  INFO: 测试进度：0/1
2025-06-12 11:18:13,732  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 11:18:13,732  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 11:18:13,779  INFO: === 初始化测试环境 ===
2025-06-12 11:18:13,780  INFO: 正在初始化设备连接...
2025-06-12 11:18:13,797  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 11:18:13,864  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON>&<PERSON>rz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 11:18:13,886  INFO: 正在创建测试报告...
2025-06-12 11:18:13,886  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 11:18:13,887  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_111813
2025-06-12 11:18:13,888  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_111813/superlink_rx_ci_test_debug_report_20250612_111813.xlsx
2025-06-12 11:18:13,889  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_111813/superlink_rx_ci_test_debug_report_20250612_111813.xlsx
2025-06-12 11:18:13,890  INFO: === 开始C/I测试 ===
2025-06-12 11:18:13,890  INFO: === 生成C/I测试用例 ===
2025-06-12 11:18:13,891  INFO: C/I测试参数配置:
2025-06-12 11:18:13,895  INFO:   SYM_RATES: [0]
2025-06-12 11:18:13,901  INFO:   S_CODE_EN: [1]
2025-06-12 11:18:13,902  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 11:18:13,902  INFO:   SNRS: [100]
2025-06-12 11:18:13,902  INFO:   DATA_LEN: [6]
2025-06-12 11:18:13,903  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 11:18:13,903  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 11:18:13,903  ERROR: 生成C/I测试用例失败: 'int' object is not iterable
2025-06-12 11:18:13,903  ERROR: 没有可执行的测试用例，测试终止
2025-06-12 11:18:13,903  INFO: === 清理测试环境 ===
2025-06-12 11:18:13,903  INFO: 设备连接已断开
2025-06-12 11:18:13,904  INFO: === 测试完成 ===
2025-06-12 11:18:13,904  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 11:18:13,904  INFO: 测试进度：1/1
2025-06-12 11:18:18,905  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 11:18:18,910  INFO: 测试完成！总共测试耗时：00:00:06
2025-06-12 11:22:50,552  INFO: 测试开始!
2025-06-12 11:22:50,554  INFO: 测试进度：0/1
2025-06-12 11:22:50,554  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 11:22:50,555  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 11:22:50,580  INFO: === 初始化测试环境 ===
2025-06-12 11:22:50,580  INFO: 正在初始化设备连接...
2025-06-12 11:22:50,603  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 11:22:50,671  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 11:22:50,680  INFO: 正在创建测试报告...
2025-06-12 11:22:50,680  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 11:22:50,681  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112250
2025-06-12 11:22:50,682  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112250/superlink_rx_ci_test_debug_report_20250612_112250.xlsx
2025-06-12 11:22:50,682  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112250/superlink_rx_ci_test_debug_report_20250612_112250.xlsx
2025-06-12 11:22:50,683  INFO: === 开始C/I测试 ===
2025-06-12 11:22:50,689  INFO: === 生成C/I测试用例 ===
2025-06-12 11:22:50,690  INFO: C/I测试参数配置:
2025-06-12 11:22:50,692  INFO:   SYM_RATES: [0]
2025-06-12 11:22:50,692  INFO:   S_CODE_EN: [1]
2025-06-12 11:22:50,693  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 11:22:50,693  INFO:   SNRS: [100]
2025-06-12 11:22:50,694  INFO:   DATA_LEN: [6]
2025-06-12 11:22:50,694  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 11:22:50,694  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 11:22:50,695  INFO: 测试用例数量(54)超过限制(5)，将只执行前5个用例
2025-06-12 11:22:50,695  INFO: 成功生成 5 个C/I测试用例
2025-06-12 11:22:50,695  INFO: 总测试用例数: 5
2025-06-12 11:22:50,695  INFO: 
=== 执行测试用例 1/5 ===
2025-06-12 11:22:50,695  INFO: 
=== 执行测试用例 2/5 ===
2025-06-12 11:22:50,700  INFO: 
=== 执行测试用例 3/5 ===
2025-06-12 11:22:50,702  INFO: 
=== 执行测试用例 4/5 ===
2025-06-12 11:22:50,703  INFO: 
=== 执行测试用例 5/5 ===
2025-06-12 11:22:50,707  INFO: === 灵敏度测试完成 ===
2025-06-12 11:22:50,708  INFO: === 清理测试环境 ===
2025-06-12 11:22:50,708  INFO: 设备连接已断开
2025-06-12 11:22:50,709  INFO: === 测试完成 ===
2025-06-12 11:22:50,709  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 11:22:50,709  INFO: 测试进度：1/1
2025-06-12 11:22:55,703  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 11:22:55,708  INFO: 测试完成！总共测试耗时：00:00:06
2025-06-12 11:23:57,720  INFO: 测试开始!
2025-06-12 11:23:57,722  INFO: 测试进度：0/1
2025-06-12 11:23:57,723  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 11:23:57,724  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 11:23:57,746  INFO: === 初始化测试环境 ===
2025-06-12 11:23:57,748  INFO: 正在初始化设备连接...
2025-06-12 11:23:57,765  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 11:23:57,830  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 11:23:57,839  INFO: 正在创建测试报告...
2025-06-12 11:23:57,839  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 11:23:57,840  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112357
2025-06-12 11:23:57,841  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112357/superlink_rx_ci_test_debug_report_20250612_112357.xlsx
2025-06-12 11:23:57,841  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112357/superlink_rx_ci_test_debug_report_20250612_112357.xlsx
2025-06-12 11:23:57,842  INFO: === 开始C/I测试 ===
2025-06-12 11:23:57,843  INFO: === 生成C/I测试用例 ===
2025-06-12 11:23:57,851  INFO: C/I测试参数配置:
2025-06-12 11:23:57,852  INFO:   SYM_RATES: [0]
2025-06-12 11:23:57,853  INFO:   S_CODE_EN: [1]
2025-06-12 11:23:57,854  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 11:23:57,855  INFO:   SNRS: [100]
2025-06-12 11:23:57,855  INFO:   DATA_LEN: [6]
2025-06-12 11:23:57,856  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 11:23:57,856  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 11:23:57,857  INFO: 成功生成 54 个C/I测试用例
2025-06-12 11:23:57,857  INFO: 总测试用例数: 54
2025-06-12 11:23:57,857  INFO: 
=== 执行测试用例 1/54 ===
2025-06-12 11:23:57,858  INFO: 
=== 执行测试用例 2/54 ===
2025-06-12 11:23:57,858  INFO: 
=== 执行测试用例 3/54 ===
2025-06-12 11:23:57,858  INFO: 
=== 执行测试用例 4/54 ===
2025-06-12 11:23:57,859  INFO: 
=== 执行测试用例 5/54 ===
2025-06-12 11:23:57,859  INFO: 
=== 执行测试用例 6/54 ===
2025-06-12 11:23:57,859  INFO: 
=== 执行测试用例 7/54 ===
2025-06-12 11:23:57,859  INFO: 
=== 执行测试用例 8/54 ===
2025-06-12 11:23:57,860  INFO: 
=== 执行测试用例 9/54 ===
2025-06-12 11:23:57,860  INFO: 
=== 执行测试用例 10/54 ===
2025-06-12 11:23:57,860  INFO: 
=== 执行测试用例 11/54 ===
2025-06-12 11:23:57,861  INFO: 
=== 执行测试用例 12/54 ===
2025-06-12 11:23:57,861  INFO: 
=== 执行测试用例 13/54 ===
2025-06-12 11:23:57,861  INFO: 
=== 执行测试用例 14/54 ===
2025-06-12 11:23:57,861  INFO: 
=== 执行测试用例 15/54 ===
2025-06-12 11:23:57,862  INFO: 
=== 执行测试用例 16/54 ===
2025-06-12 11:23:57,862  INFO: 
=== 执行测试用例 17/54 ===
2025-06-12 11:23:57,875  INFO: 
=== 执行测试用例 18/54 ===
2025-06-12 11:23:57,876  INFO: 
=== 执行测试用例 19/54 ===
2025-06-12 11:23:57,876  INFO: 
=== 执行测试用例 20/54 ===
2025-06-12 11:23:57,876  INFO: 
=== 执行测试用例 21/54 ===
2025-06-12 11:23:57,877  INFO: 
=== 执行测试用例 22/54 ===
2025-06-12 11:23:57,877  INFO: 
=== 执行测试用例 23/54 ===
2025-06-12 11:23:57,877  INFO: 
=== 执行测试用例 24/54 ===
2025-06-12 11:23:57,881  INFO: 
=== 执行测试用例 25/54 ===
2025-06-12 11:23:57,881  INFO: 
=== 执行测试用例 26/54 ===
2025-06-12 11:23:57,882  INFO: 
=== 执行测试用例 27/54 ===
2025-06-12 11:23:57,882  INFO: 
=== 执行测试用例 28/54 ===
2025-06-12 11:23:57,882  INFO: 
=== 执行测试用例 29/54 ===
2025-06-12 11:23:57,882  INFO: 
=== 执行测试用例 30/54 ===
2025-06-12 11:23:57,883  INFO: 
=== 执行测试用例 31/54 ===
2025-06-12 11:23:57,883  INFO: 
=== 执行测试用例 32/54 ===
2025-06-12 11:23:57,883  INFO: 
=== 执行测试用例 33/54 ===
2025-06-12 11:23:57,883  INFO: 
=== 执行测试用例 34/54 ===
2025-06-12 11:23:57,884  INFO: 
=== 执行测试用例 35/54 ===
2025-06-12 11:23:57,884  INFO: 
=== 执行测试用例 36/54 ===
2025-06-12 11:23:57,884  INFO: 
=== 执行测试用例 37/54 ===
2025-06-12 11:23:57,884  INFO: 
=== 执行测试用例 38/54 ===
2025-06-12 11:23:57,884  INFO: 
=== 执行测试用例 39/54 ===
2025-06-12 11:23:57,884  INFO: 
=== 执行测试用例 40/54 ===
2025-06-12 11:23:57,885  INFO: 
=== 执行测试用例 41/54 ===
2025-06-12 11:23:57,885  INFO: 
=== 执行测试用例 42/54 ===
2025-06-12 11:23:57,885  INFO: 
=== 执行测试用例 43/54 ===
2025-06-12 11:23:57,885  INFO: 
=== 执行测试用例 44/54 ===
2025-06-12 11:23:57,885  INFO: 
=== 执行测试用例 45/54 ===
2025-06-12 11:23:57,886  INFO: 
=== 执行测试用例 46/54 ===
2025-06-12 11:23:57,886  INFO: 
=== 执行测试用例 47/54 ===
2025-06-12 11:23:57,886  INFO: 
=== 执行测试用例 48/54 ===
2025-06-12 11:23:57,888  INFO: 
=== 执行测试用例 49/54 ===
2025-06-12 11:23:57,889  INFO: 
=== 执行测试用例 50/54 ===
2025-06-12 11:23:57,895  INFO: 
=== 执行测试用例 51/54 ===
2025-06-12 11:23:57,904  INFO: 
=== 执行测试用例 52/54 ===
2025-06-12 11:23:57,905  INFO: 
=== 执行测试用例 53/54 ===
2025-06-12 11:23:57,906  INFO: 
=== 执行测试用例 54/54 ===
2025-06-12 11:23:57,906  INFO: === 灵敏度测试完成 ===
2025-06-12 11:23:57,906  INFO: === 清理测试环境 ===
2025-06-12 11:23:57,906  INFO: 设备连接已断开
2025-06-12 11:23:57,906  INFO: === 测试完成 ===
2025-06-12 11:23:57,907  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 11:23:57,907  INFO: 测试进度：1/1
2025-06-12 11:24:02,860  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 11:24:02,864  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-12 11:25:54,349  INFO: 测试开始!
2025-06-12 11:25:54,351  INFO: 测试进度：0/1
2025-06-12 11:25:54,352  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 11:25:54,352  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 11:25:54,371  INFO: === 初始化测试环境 ===
2025-06-12 11:25:54,371  INFO: 正在初始化设备连接...
2025-06-12 11:25:54,388  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 11:25:54,455  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 11:25:54,464  INFO: 正在创建测试报告...
2025-06-12 11:25:54,464  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 11:25:54,465  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112554
2025-06-12 11:25:54,466  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112554/superlink_rx_ci_test_debug_report_20250612_112554.xlsx
2025-06-12 11:25:54,466  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112554/superlink_rx_ci_test_debug_report_20250612_112554.xlsx
2025-06-12 11:25:54,467  INFO: === 开始C/I测试 ===
2025-06-12 11:25:54,471  INFO: === 生成C/I测试用例 ===
2025-06-12 11:25:54,473  INFO: C/I测试参数配置:
2025-06-12 11:25:54,474  INFO:   SYM_RATES: [1]
2025-06-12 11:25:54,478  INFO:   S_CODE_EN: [1]
2025-06-12 11:25:54,481  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 11:25:54,482  INFO:   SNRS: [100]
2025-06-12 11:25:54,482  INFO:   DATA_LEN: [6]
2025-06-12 11:25:54,483  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 11:25:54,483  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 11:25:54,483  INFO: 成功生成 54 个C/I测试用例
2025-06-12 11:25:54,483  INFO: 总测试用例数: 54
2025-06-12 11:25:54,483  INFO: 
=== 执行测试用例 1/54 ===
2025-06-12 11:25:54,483  INFO: 
=== 执行测试用例 2/54 ===
2025-06-12 11:25:54,484  INFO: 
=== 执行测试用例 3/54 ===
2025-06-12 11:25:54,485  INFO: 
=== 执行测试用例 4/54 ===
2025-06-12 11:25:54,485  INFO: 
=== 执行测试用例 5/54 ===
2025-06-12 11:25:54,485  INFO: 
=== 执行测试用例 6/54 ===
2025-06-12 11:25:54,485  INFO: 
=== 执行测试用例 7/54 ===
2025-06-12 11:25:54,485  INFO: 
=== 执行测试用例 8/54 ===
2025-06-12 11:25:54,486  INFO: 
=== 执行测试用例 9/54 ===
2025-06-12 11:25:54,486  INFO: 
=== 执行测试用例 10/54 ===
2025-06-12 11:25:54,486  INFO: 
=== 执行测试用例 11/54 ===
2025-06-12 11:25:54,486  INFO: 
=== 执行测试用例 12/54 ===
2025-06-12 11:25:54,486  INFO: 
=== 执行测试用例 13/54 ===
2025-06-12 11:25:54,487  INFO: 
=== 执行测试用例 14/54 ===
2025-06-12 11:25:54,487  INFO: 
=== 执行测试用例 15/54 ===
2025-06-12 11:25:54,487  INFO: 
=== 执行测试用例 16/54 ===
2025-06-12 11:25:54,487  INFO: 
=== 执行测试用例 17/54 ===
2025-06-12 11:25:54,488  INFO: 
=== 执行测试用例 18/54 ===
2025-06-12 11:25:54,488  INFO: 
=== 执行测试用例 19/54 ===
2025-06-12 11:25:54,488  INFO: 
=== 执行测试用例 20/54 ===
2025-06-12 11:25:54,489  INFO: 
=== 执行测试用例 21/54 ===
2025-06-12 11:25:54,509  INFO: 
=== 执行测试用例 22/54 ===
2025-06-12 11:25:54,510  INFO: 
=== 执行测试用例 23/54 ===
2025-06-12 11:25:54,516  INFO: 
=== 执行测试用例 24/54 ===
2025-06-12 11:25:54,517  INFO: 
=== 执行测试用例 25/54 ===
2025-06-12 11:25:54,518  INFO: 
=== 执行测试用例 26/54 ===
2025-06-12 11:25:54,519  INFO: 
=== 执行测试用例 27/54 ===
2025-06-12 11:25:54,519  INFO: 
=== 执行测试用例 28/54 ===
2025-06-12 11:25:54,520  INFO: 
=== 执行测试用例 29/54 ===
2025-06-12 11:25:54,520  INFO: 
=== 执行测试用例 30/54 ===
2025-06-12 11:25:54,521  INFO: 
=== 执行测试用例 31/54 ===
2025-06-12 11:25:54,521  INFO: 
=== 执行测试用例 32/54 ===
2025-06-12 11:25:54,521  INFO: 
=== 执行测试用例 33/54 ===
2025-06-12 11:25:54,521  INFO: 
=== 执行测试用例 34/54 ===
2025-06-12 11:25:54,522  INFO: 
=== 执行测试用例 35/54 ===
2025-06-12 11:25:54,522  INFO: 
=== 执行测试用例 36/54 ===
2025-06-12 11:25:54,522  INFO: 
=== 执行测试用例 37/54 ===
2025-06-12 11:25:54,523  INFO: 
=== 执行测试用例 38/54 ===
2025-06-12 11:25:54,523  INFO: 
=== 执行测试用例 39/54 ===
2025-06-12 11:25:54,523  INFO: 
=== 执行测试用例 40/54 ===
2025-06-12 11:25:54,523  INFO: 
=== 执行测试用例 41/54 ===
2025-06-12 11:25:54,524  INFO: 
=== 执行测试用例 42/54 ===
2025-06-12 11:25:54,524  INFO: 
=== 执行测试用例 43/54 ===
2025-06-12 11:25:54,524  INFO: 
=== 执行测试用例 44/54 ===
2025-06-12 11:25:54,524  INFO: 
=== 执行测试用例 45/54 ===
2025-06-12 11:25:54,524  INFO: 
=== 执行测试用例 46/54 ===
2025-06-12 11:25:54,524  INFO: 
=== 执行测试用例 47/54 ===
2025-06-12 11:25:54,525  INFO: 
=== 执行测试用例 48/54 ===
2025-06-12 11:25:54,525  INFO: 
=== 执行测试用例 49/54 ===
2025-06-12 11:25:54,525  INFO: 
=== 执行测试用例 50/54 ===
2025-06-12 11:25:54,526  INFO: 
=== 执行测试用例 51/54 ===
2025-06-12 11:25:54,534  INFO: 
=== 执行测试用例 52/54 ===
2025-06-12 11:25:54,540  INFO: 
=== 执行测试用例 53/54 ===
2025-06-12 11:25:54,559  INFO: 
=== 执行测试用例 54/54 ===
2025-06-12 11:25:54,562  INFO: === 灵敏度测试完成 ===
2025-06-12 11:25:54,563  INFO: === 清理测试环境 ===
2025-06-12 11:25:54,563  INFO: 设备连接已断开
2025-06-12 11:25:54,564  INFO: === 测试完成 ===
2025-06-12 11:25:54,564  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 11:25:54,565  INFO: 测试进度：1/1
2025-06-12 11:25:59,483  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 11:25:59,488  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-12 11:26:56,361  INFO: 测试开始!
2025-06-12 11:26:56,363  INFO: 测试进度：0/1
2025-06-12 11:26:56,365  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 11:26:56,366  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 11:26:56,382  INFO: === 初始化测试环境 ===
2025-06-12 11:26:56,382  INFO: 正在初始化设备连接...
2025-06-12 11:26:56,400  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 11:26:56,751  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 11:26:56,760  INFO: 正在创建测试报告...
2025-06-12 11:26:56,760  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 11:26:56,761  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112656
2025-06-12 11:26:56,762  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112656/superlink_rx_ci_test_debug_report_20250612_112656.xlsx
2025-06-12 11:26:56,762  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_112656/superlink_rx_ci_test_debug_report_20250612_112656.xlsx
2025-06-12 11:26:56,763  INFO: === 开始C/I测试 ===
2025-06-12 11:26:56,771  INFO: === 生成C/I测试用例 ===
2025-06-12 11:26:56,772  INFO: C/I测试参数配置:
2025-06-12 11:26:56,773  INFO:   SYM_RATES: [1]
2025-06-12 11:26:56,773  INFO:   S_CODE_EN: [1]
2025-06-12 11:26:56,773  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 11:26:56,774  INFO:   SNRS: [100]
2025-06-12 11:26:56,774  INFO:   DATA_LEN: [6]
2025-06-12 11:26:56,775  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 11:26:56,775  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 11:26:56,778  INFO: 成功生成 54 个C/I测试用例
2025-06-12 11:26:56,779  INFO: 总测试用例数: 54
2025-06-12 11:26:56,780  INFO: 
=== 执行测试用例 1/54 ===
2025-06-12 11:26:56,781  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 11:26:56,782  INFO: 
=== 执行测试用例 2/54 ===
2025-06-12 11:26:56,783  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 2}
2025-06-12 11:26:56,783  INFO: 
=== 执行测试用例 3/54 ===
2025-06-12 11:26:56,788  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -2}
2025-06-12 11:26:56,788  INFO: 
=== 执行测试用例 4/54 ===
2025-06-12 11:26:56,788  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 11:26:56,789  INFO: 
=== 执行测试用例 5/54 ===
2025-06-12 11:26:56,789  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 11:26:56,790  INFO: 
=== 执行测试用例 6/54 ===
2025-06-12 11:26:56,791  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 6}
2025-06-12 11:26:56,797  INFO: 
=== 执行测试用例 7/54 ===
2025-06-12 11:26:56,797  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -6}
2025-06-12 11:26:56,799  INFO: 
=== 执行测试用例 8/54 ===
2025-06-12 11:26:56,799  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 11:26:56,800  INFO: 
=== 执行测试用例 9/54 ===
2025-06-12 11:26:56,800  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 11:26:56,800  INFO: 
=== 执行测试用例 10/54 ===
2025-06-12 11:26:56,801  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 11:26:56,801  INFO: 
=== 执行测试用例 11/54 ===
2025-06-12 11:26:56,801  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 2}
2025-06-12 11:26:56,801  INFO: 
=== 执行测试用例 12/54 ===
2025-06-12 11:26:56,801  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -2}
2025-06-12 11:26:56,802  INFO: 
=== 执行测试用例 13/54 ===
2025-06-12 11:26:56,802  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 11:26:56,802  INFO: 
=== 执行测试用例 14/54 ===
2025-06-12 11:26:56,802  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 11:26:56,802  INFO: 
=== 执行测试用例 15/54 ===
2025-06-12 11:26:56,802  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 6}
2025-06-12 11:26:56,802  INFO: 
=== 执行测试用例 16/54 ===
2025-06-12 11:26:56,803  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -6}
2025-06-12 11:26:56,803  INFO: 
=== 执行测试用例 17/54 ===
2025-06-12 11:26:56,803  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 11:26:56,803  INFO: 
=== 执行测试用例 18/54 ===
2025-06-12 11:26:56,803  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 11:26:56,803  INFO: 
=== 执行测试用例 19/54 ===
2025-06-12 11:26:56,804  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 11:26:56,804  INFO: 
=== 执行测试用例 20/54 ===
2025-06-12 11:26:56,804  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 2}
2025-06-12 11:26:56,804  INFO: 
=== 执行测试用例 21/54 ===
2025-06-12 11:26:56,804  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -2}
2025-06-12 11:26:56,805  INFO: 
=== 执行测试用例 22/54 ===
2025-06-12 11:26:56,805  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 11:26:56,805  INFO: 
=== 执行测试用例 23/54 ===
2025-06-12 11:26:56,805  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 11:26:56,805  INFO: 
=== 执行测试用例 24/54 ===
2025-06-12 11:26:56,805  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 6}
2025-06-12 11:26:56,806  INFO: 
=== 执行测试用例 25/54 ===
2025-06-12 11:26:56,806  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -6}
2025-06-12 11:26:56,806  INFO: 
=== 执行测试用例 26/54 ===
2025-06-12 11:26:56,806  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 11:26:56,806  INFO: 
=== 执行测试用例 27/54 ===
2025-06-12 11:26:56,806  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 11:26:56,807  INFO: 
=== 执行测试用例 28/54 ===
2025-06-12 11:26:56,831  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 11:26:56,831  INFO: 
=== 执行测试用例 29/54 ===
2025-06-12 11:26:56,831  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 2}
2025-06-12 11:26:56,832  INFO: 
=== 执行测试用例 30/54 ===
2025-06-12 11:26:56,833  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -2}
2025-06-12 11:26:56,835  INFO: 
=== 执行测试用例 31/54 ===
2025-06-12 11:26:56,835  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 11:26:56,835  INFO: 
=== 执行测试用例 32/54 ===
2025-06-12 11:26:56,836  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 11:26:56,836  INFO: 
=== 执行测试用例 33/54 ===
2025-06-12 11:26:56,836  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 6}
2025-06-12 11:26:56,836  INFO: 
=== 执行测试用例 34/54 ===
2025-06-12 11:26:56,836  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -6}
2025-06-12 11:26:56,837  INFO: 
=== 执行测试用例 35/54 ===
2025-06-12 11:26:56,837  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 11:26:56,837  INFO: 
=== 执行测试用例 36/54 ===
2025-06-12 11:26:56,837  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 11:26:56,837  INFO: 
=== 执行测试用例 37/54 ===
2025-06-12 11:26:56,837  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 11:26:56,838  INFO: 
=== 执行测试用例 38/54 ===
2025-06-12 11:26:56,838  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 2}
2025-06-12 11:26:56,838  INFO: 
=== 执行测试用例 39/54 ===
2025-06-12 11:26:56,838  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -2}
2025-06-12 11:26:56,838  INFO: 
=== 执行测试用例 40/54 ===
2025-06-12 11:26:56,838  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 11:26:56,839  INFO: 
=== 执行测试用例 41/54 ===
2025-06-12 11:26:56,839  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 11:26:56,839  INFO: 
=== 执行测试用例 42/54 ===
2025-06-12 11:26:56,839  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 6}
2025-06-12 11:26:56,839  INFO: 
=== 执行测试用例 43/54 ===
2025-06-12 11:26:56,839  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -6}
2025-06-12 11:26:56,839  INFO: 
=== 执行测试用例 44/54 ===
2025-06-12 11:26:56,840  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 11:26:56,840  INFO: 
=== 执行测试用例 45/54 ===
2025-06-12 11:26:56,840  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 11:26:56,840  INFO: 
=== 执行测试用例 46/54 ===
2025-06-12 11:26:56,840  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 11:26:56,841  INFO: 
=== 执行测试用例 47/54 ===
2025-06-12 11:26:56,841  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 2}
2025-06-12 11:26:56,841  INFO: 
=== 执行测试用例 48/54 ===
2025-06-12 11:26:56,841  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -2}
2025-06-12 11:26:56,841  INFO: 
=== 执行测试用例 49/54 ===
2025-06-12 11:26:56,842  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 11:26:56,865  INFO: 
=== 执行测试用例 50/54 ===
2025-06-12 11:26:56,866  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 11:26:56,866  INFO: 
=== 执行测试用例 51/54 ===
2025-06-12 11:26:56,866  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 6}
2025-06-12 11:26:56,866  INFO: 
=== 执行测试用例 52/54 ===
2025-06-12 11:26:56,868  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -6}
2025-06-12 11:26:56,868  INFO: 
=== 执行测试用例 53/54 ===
2025-06-12 11:26:56,869  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 11:26:56,869  INFO: 
=== 执行测试用例 54/54 ===
2025-06-12 11:26:56,869  INFO: {'data_len': 6, 'sym_rate': 1, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 2, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 11:26:56,870  INFO: === 灵敏度测试完成 ===
2025-06-12 11:26:56,870  INFO: === 清理测试环境 ===
2025-06-12 11:26:56,870  INFO: 设备连接已断开
2025-06-12 11:26:56,870  INFO: === 测试完成 ===
2025-06-12 11:26:56,870  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 11:26:56,870  INFO: 测试进度：1/1
2025-06-12 11:27:01,801  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 11:27:01,822  INFO: 测试完成！总共测试耗时：00:00:06
2025-06-12 13:25:37,435  INFO: 测试开始!
2025-06-12 13:25:37,436  INFO: 测试进度：0/1
2025-06-12 13:25:37,436  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 13:25:37,437  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 13:25:37,456  INFO: === 初始化测试环境 ===
2025-06-12 13:25:37,456  INFO: 正在初始化设备连接...
2025-06-12 13:25:42,461  ERROR: connect bluetooth_tester timeout
2025-06-12 13:25:42,461  ERROR: 设备初始化失败: connect bluetooth_tester timeout
2025-06-12 13:25:42,469  ERROR: 测试终止！测试错误：connect bluetooth_tester timeout
2025-06-12 13:30:34,389  INFO: 测试开始!
2025-06-12 13:30:34,393  INFO: 测试进度：0/1
2025-06-12 13:30:34,394  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 13:30:34,397  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 13:30:34,414  INFO: === 初始化测试环境 ===
2025-06-12 13:30:34,414  INFO: 正在初始化设备连接...
2025-06-12 13:30:34,431  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 13:30:34,506  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 13:30:34,515  INFO: 正在创建测试报告...
2025-06-12 13:30:34,515  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 13:30:34,516  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_133034
2025-06-12 13:30:34,518  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_133034/superlink_rx_ci_test_debug_report_20250612_133034.xlsx
2025-06-12 13:30:34,520  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_133034/superlink_rx_ci_test_debug_report_20250612_133034.xlsx
2025-06-12 13:30:34,525  INFO: === 开始C/I测试 ===
2025-06-12 13:30:34,530  INFO: === 生成C/I测试用例 ===
2025-06-12 13:30:34,531  INFO: C/I测试参数配置:
2025-06-12 13:30:34,532  INFO:   SYM_RATES: [2]
2025-06-12 13:30:34,533  INFO:   S_CODE_EN: [1]
2025-06-12 13:30:34,534  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 13:30:34,535  INFO:   SNRS: [100]
2025-06-12 13:30:34,536  INFO:   DATA_LEN: [6]
2025-06-12 13:30:34,537  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 13:30:34,538  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 13:30:34,539  INFO: 成功生成 54 个C/I测试用例
2025-06-12 13:30:34,544  INFO: 总测试用例数: 54
2025-06-12 13:30:34,545  INFO: 
=== 执行测试用例 1/54 ===
2025-06-12 13:30:34,546  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 13:30:34,546  INFO: 
=== 执行测试用例 2/54 ===
2025-06-12 13:30:34,547  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 13:30:34,547  INFO: 
=== 执行测试用例 3/54 ===
2025-06-12 13:30:34,547  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 13:30:34,548  INFO: 
=== 执行测试用例 4/54 ===
2025-06-12 13:30:34,548  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 13:30:34,548  INFO: 
=== 执行测试用例 5/54 ===
2025-06-12 13:30:34,548  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 13:30:34,548  INFO: 
=== 执行测试用例 6/54 ===
2025-06-12 13:30:34,549  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 12}
2025-06-12 13:30:34,549  INFO: 
=== 执行测试用例 7/54 ===
2025-06-12 13:30:34,549  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -12}
2025-06-12 13:30:34,549  INFO: 
=== 执行测试用例 8/54 ===
2025-06-12 13:30:34,550  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 16}
2025-06-12 13:30:34,561  INFO: 
=== 执行测试用例 9/54 ===
2025-06-12 13:30:34,561  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -16}
2025-06-12 13:30:34,561  INFO: 
=== 执行测试用例 10/54 ===
2025-06-12 13:30:34,562  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 13:30:34,563  INFO: 
=== 执行测试用例 11/54 ===
2025-06-12 13:30:34,565  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 13:30:34,565  INFO: 
=== 执行测试用例 12/54 ===
2025-06-12 13:30:34,566  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 13:30:34,566  INFO: 
=== 执行测试用例 13/54 ===
2025-06-12 13:30:34,566  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 13:30:34,567  INFO: 
=== 执行测试用例 14/54 ===
2025-06-12 13:30:34,567  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 13:30:34,567  INFO: 
=== 执行测试用例 15/54 ===
2025-06-12 13:30:34,568  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 12}
2025-06-12 13:30:34,568  INFO: 
=== 执行测试用例 16/54 ===
2025-06-12 13:30:34,568  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -12}
2025-06-12 13:30:34,568  INFO: 
=== 执行测试用例 17/54 ===
2025-06-12 13:30:34,568  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 16}
2025-06-12 13:30:34,568  INFO: 
=== 执行测试用例 18/54 ===
2025-06-12 13:30:34,568  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -16}
2025-06-12 13:30:34,571  INFO: 
=== 执行测试用例 19/54 ===
2025-06-12 13:30:34,571  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 13:30:34,572  INFO: 
=== 执行测试用例 20/54 ===
2025-06-12 13:30:34,573  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 13:30:34,573  INFO: 
=== 执行测试用例 21/54 ===
2025-06-12 13:30:34,573  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 13:30:34,574  INFO: 
=== 执行测试用例 22/54 ===
2025-06-12 13:30:34,574  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 13:30:34,575  INFO: 
=== 执行测试用例 23/54 ===
2025-06-12 13:30:34,575  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 13:30:34,575  INFO: 
=== 执行测试用例 24/54 ===
2025-06-12 13:30:34,576  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 12}
2025-06-12 13:30:34,576  INFO: 
=== 执行测试用例 25/54 ===
2025-06-12 13:30:34,576  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -12}
2025-06-12 13:30:34,576  INFO: 
=== 执行测试用例 26/54 ===
2025-06-12 13:30:34,577  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 16}
2025-06-12 13:30:34,577  INFO: 
=== 执行测试用例 27/54 ===
2025-06-12 13:30:34,578  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 4, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -16}
2025-06-12 13:30:34,578  INFO: 
=== 执行测试用例 28/54 ===
2025-06-12 13:30:34,578  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 13:30:34,578  INFO: 
=== 执行测试用例 29/54 ===
2025-06-12 13:30:34,579  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 13:30:34,579  INFO: 
=== 执行测试用例 30/54 ===
2025-06-12 13:30:34,579  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 13:30:34,579  INFO: 
=== 执行测试用例 31/54 ===
2025-06-12 13:30:34,579  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 13:30:34,579  INFO: 
=== 执行测试用例 32/54 ===
2025-06-12 13:30:34,579  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 13:30:34,580  INFO: 
=== 执行测试用例 33/54 ===
2025-06-12 13:30:34,580  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 12}
2025-06-12 13:30:34,580  INFO: 
=== 执行测试用例 34/54 ===
2025-06-12 13:30:34,580  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -12}
2025-06-12 13:30:34,580  INFO: 
=== 执行测试用例 35/54 ===
2025-06-12 13:30:34,581  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 16}
2025-06-12 13:30:34,581  INFO: 
=== 执行测试用例 36/54 ===
2025-06-12 13:30:34,581  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 5, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -16}
2025-06-12 13:30:34,585  INFO: 
=== 执行测试用例 37/54 ===
2025-06-12 13:30:34,609  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 13:30:34,615  INFO: 
=== 执行测试用例 38/54 ===
2025-06-12 13:30:34,616  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 13:30:34,616  INFO: 
=== 执行测试用例 39/54 ===
2025-06-12 13:30:34,616  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 13:30:34,617  INFO: 
=== 执行测试用例 40/54 ===
2025-06-12 13:30:34,617  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 13:30:34,617  INFO: 
=== 执行测试用例 41/54 ===
2025-06-12 13:30:34,618  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 13:30:34,618  INFO: 
=== 执行测试用例 42/54 ===
2025-06-12 13:30:34,618  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 12}
2025-06-12 13:30:34,618  INFO: 
=== 执行测试用例 43/54 ===
2025-06-12 13:30:34,618  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -12}
2025-06-12 13:30:34,619  INFO: 
=== 执行测试用例 44/54 ===
2025-06-12 13:30:34,619  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 16}
2025-06-12 13:30:34,619  INFO: 
=== 执行测试用例 45/54 ===
2025-06-12 13:30:34,619  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 6, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -16}
2025-06-12 13:30:34,619  INFO: 
=== 执行测试用例 46/54 ===
2025-06-12 13:30:34,619  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 13:30:34,620  INFO: 
=== 执行测试用例 47/54 ===
2025-06-12 13:30:34,620  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': 1, 'inf_fre_offset': 4}
2025-06-12 13:30:34,620  INFO: 
=== 执行测试用例 48/54 ===
2025-06-12 13:30:34,620  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 1, 'inf_fre_calc_op': -1, 'inf_fre_offset': -4}
2025-06-12 13:30:34,620  INFO: 
=== 执行测试用例 49/54 ===
2025-06-12 13:30:34,620  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': 1, 'inf_fre_offset': 8}
2025-06-12 13:30:34,621  INFO: 
=== 执行测试用例 50/54 ===
2025-06-12 13:30:34,621  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 2, 'inf_fre_calc_op': -1, 'inf_fre_offset': -8}
2025-06-12 13:30:34,621  INFO: 
=== 执行测试用例 51/54 ===
2025-06-12 13:30:34,621  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': 1, 'inf_fre_offset': 12}
2025-06-12 13:30:34,621  INFO: 
=== 执行测试用例 52/54 ===
2025-06-12 13:30:34,621  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 3, 'inf_fre_calc_op': -1, 'inf_fre_offset': -12}
2025-06-12 13:30:34,621  INFO: 
=== 执行测试用例 53/54 ===
2025-06-12 13:30:34,622  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': 1, 'inf_fre_offset': 16}
2025-06-12 13:30:34,622  INFO: 
=== 执行测试用例 54/54 ===
2025-06-12 13:30:34,622  INFO: {'data_len': 6, 'sym_rate': 2, 's_code_en': 1, 'scode_rate': 7, 'snr': 100, 'signal_ch': 3, 'inf_fre_offset_point': 4, 'inf_fre_calc_op': -1, 'inf_fre_offset': -16}
2025-06-12 13:30:34,622  INFO: === 灵敏度测试完成 ===
2025-06-12 13:30:34,622  INFO: === 清理测试环境 ===
2025-06-12 13:30:34,623  INFO: 设备连接已断开
2025-06-12 13:30:34,623  INFO: === 测试完成 ===
2025-06-12 13:30:34,623  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 13:30:34,623  INFO: 测试进度：1/1
2025-06-12 13:30:39,549  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 13:30:39,553  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-12 14:03:32,033  INFO: 测试开始!
2025-06-12 14:03:32,034  INFO: 测试进度：0/1
2025-06-12 14:03:32,035  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:03:32,035  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:03:32,057  INFO: === 初始化测试环境 ===
2025-06-12 14:03:32,059  INFO: 正在初始化设备连接...
2025-06-12 14:03:32,077  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:03:32,144  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:03:32,153  INFO: 正在创建测试报告...
2025-06-12 14:03:32,153  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:03:32,155  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_140332
2025-06-12 14:03:32,156  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_140332/superlink_rx_ci_test_debug_report_20250612_140332.xlsx
2025-06-12 14:03:32,158  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_140332/superlink_rx_ci_test_debug_report_20250612_140332.xlsx
2025-06-12 14:03:32,159  INFO: === 开始C/I测试 ===
2025-06-12 14:03:32,160  INFO: === 生成C/I测试用例 ===
2025-06-12 14:03:32,160  INFO: C/I测试参数配置:
2025-06-12 14:03:32,161  INFO:   SYM_RATES: [0]
2025-06-12 14:03:32,161  INFO:   S_CODE_EN: [1]
2025-06-12 14:03:32,166  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:03:32,167  INFO:   SNRS: [100]
2025-06-12 14:03:32,167  INFO:   DATA_LEN: [6]
2025-06-12 14:03:32,167  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:03:32,167  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:03:32,168  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:03:32,168  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:03:32,168  INFO: 总测试用例数: 1
2025-06-12 14:03:32,168  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:03:32,168  INFO: 开始配置VSG信号发生器...
2025-06-12 14:03:32,841  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:03:32,841  INFO: === 灵敏度测试完成 ===
2025-06-12 14:03:32,843  INFO: === 清理测试环境 ===
2025-06-12 14:03:32,843  INFO: 设备连接已断开
2025-06-12 14:03:32,844  INFO: === 测试完成 ===
2025-06-12 14:03:32,844  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:03:32,844  INFO: 测试进度：1/1
2025-06-12 14:03:37,849  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:03:37,854  INFO: 测试完成！总共测试耗时：00:00:05
2025-06-12 14:05:24,389  INFO: 测试开始!
2025-06-12 14:05:24,390  INFO: 测试进度：0/1
2025-06-12 14:05:24,391  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:05:24,394  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:05:24,408  INFO: === 初始化测试环境 ===
2025-06-12 14:05:24,408  INFO: 正在初始化设备连接...
2025-06-12 14:05:24,427  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:05:24,494  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:05:24,503  INFO: 正在创建测试报告...
2025-06-12 14:05:24,505  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:05:24,506  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_140524
2025-06-12 14:05:24,506  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_140524/superlink_rx_ci_test_debug_report_20250612_140524.xlsx
2025-06-12 14:05:24,507  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_140524/superlink_rx_ci_test_debug_report_20250612_140524.xlsx
2025-06-12 14:05:24,507  INFO: === 开始C/I测试 ===
2025-06-12 14:05:24,507  INFO: === 生成C/I测试用例 ===
2025-06-12 14:05:24,508  INFO: C/I测试参数配置:
2025-06-12 14:05:24,508  INFO:   SYM_RATES: [0]
2025-06-12 14:05:24,508  INFO:   S_CODE_EN: [1]
2025-06-12 14:05:24,509  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:05:24,509  INFO:   SNRS: [100]
2025-06-12 14:05:24,513  INFO:   DATA_LEN: [6]
2025-06-12 14:05:24,518  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:05:24,518  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:05:24,519  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:05:24,519  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:05:24,519  INFO: 总测试用例数: 1
2025-06-12 14:05:24,519  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:05:24,519  INFO: 开始配置VSG信号发生器...
2025-06-12 14:05:25,134  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:05:25,134  INFO: === 灵敏度测试完成 ===
2025-06-12 14:05:25,136  INFO: === 清理测试环境 ===
2025-06-12 14:05:25,137  INFO: 设备连接已断开
2025-06-12 14:05:25,137  INFO: === 测试完成 ===
2025-06-12 14:05:25,137  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:05:25,138  INFO: 测试进度：1/1
2025-06-12 14:05:30,140  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:05:30,143  INFO: 测试完成！总共测试耗时：00:00:06
2025-06-12 14:16:47,213  INFO: 测试开始!
2025-06-12 14:16:47,216  INFO: 测试进度：0/1
2025-06-12 14:16:47,221  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:16:47,221  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:16:47,240  INFO: === 初始化测试环境 ===
2025-06-12 14:16:47,240  INFO: 正在初始化设备连接...
2025-06-12 14:16:47,259  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:16:47,327  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:16:47,336  INFO: 正在创建测试报告...
2025-06-12 14:16:47,337  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:16:47,337  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_141647
2025-06-12 14:16:47,337  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_141647/superlink_rx_ci_test_debug_report_20250612_141647.xlsx
2025-06-12 14:16:47,338  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_141647/superlink_rx_ci_test_debug_report_20250612_141647.xlsx
2025-06-12 14:16:47,339  INFO: === 开始C/I测试 ===
2025-06-12 14:16:47,339  INFO: === 生成C/I测试用例 ===
2025-06-12 14:16:47,339  INFO: C/I测试参数配置:
2025-06-12 14:16:47,339  INFO:   SYM_RATES: [0]
2025-06-12 14:16:47,339  INFO:   S_CODE_EN: [1]
2025-06-12 14:16:47,339  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:16:47,340  INFO:   SNRS: [100]
2025-06-12 14:16:47,340  INFO:   DATA_LEN: [6]
2025-06-12 14:16:47,340  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:16:47,340  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:16:47,340  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:16:47,341  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:16:47,341  INFO: 总测试用例数: 1
2025-06-12 14:16:47,341  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:16:47,350  INFO: 开始配置VSG信号发生器...
2025-06-12 14:16:48,615  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:16:48,616  INFO: === 灵敏度测试完成 ===
2025-06-12 14:16:48,618  INFO: === 清理测试环境 ===
2025-06-12 14:16:48,618  INFO: 设备连接已断开
2025-06-12 14:16:48,619  INFO: === 测试完成 ===
2025-06-12 14:16:48,619  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:16:48,619  INFO: 测试进度：1/1
2025-06-12 14:16:53,619  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:16:53,623  INFO: 测试完成！总共测试耗时：00:00:06
2025-06-12 14:18:44,534  INFO: 测试开始!
2025-06-12 14:18:44,536  INFO: 测试进度：0/1
2025-06-12 14:18:44,537  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:18:44,537  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:18:44,554  INFO: === 初始化测试环境 ===
2025-06-12 14:18:44,554  INFO: 正在初始化设备连接...
2025-06-12 14:18:44,573  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:18:44,638  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:18:44,652  INFO: 正在创建测试报告...
2025-06-12 14:18:44,653  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:18:44,653  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_141844
2025-06-12 14:18:44,653  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_141844/superlink_rx_ci_test_debug_report_20250612_141844.xlsx
2025-06-12 14:18:44,653  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_141844/superlink_rx_ci_test_debug_report_20250612_141844.xlsx
2025-06-12 14:18:44,653  INFO: === 开始C/I测试 ===
2025-06-12 14:18:44,654  INFO: === 生成C/I测试用例 ===
2025-06-12 14:18:44,654  INFO: C/I测试参数配置:
2025-06-12 14:18:44,654  INFO:   SYM_RATES: [0]
2025-06-12 14:18:44,654  INFO:   S_CODE_EN: [1]
2025-06-12 14:18:44,654  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:18:44,655  INFO:   SNRS: [100]
2025-06-12 14:18:44,655  INFO:   DATA_LEN: [6]
2025-06-12 14:18:44,657  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:18:44,662  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:18:44,666  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:18:44,667  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:18:44,667  INFO: 总测试用例数: 1
2025-06-12 14:18:44,667  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:18:44,668  INFO: 开始配置VSG信号发生器...
2025-06-12 14:18:46,172  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:18:46,173  INFO: === 灵敏度测试完成 ===
2025-06-12 14:18:46,175  INFO: === 清理测试环境 ===
2025-06-12 14:18:46,176  INFO: 设备连接已断开
2025-06-12 14:18:46,176  INFO: === 测试完成 ===
2025-06-12 14:18:46,177  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:18:46,177  INFO: 测试进度：1/1
2025-06-12 14:18:51,180  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:18:51,184  INFO: 测试完成！总共测试耗时：00:00:06
2025-06-12 14:20:43,835  INFO: 测试开始!
2025-06-12 14:20:43,837  INFO: 测试进度：0/1
2025-06-12 14:20:43,838  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:20:43,838  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:20:43,856  INFO: === 初始化测试环境 ===
2025-06-12 14:20:43,856  INFO: 正在初始化设备连接...
2025-06-12 14:20:43,874  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:20:43,940  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:20:43,949  INFO: 正在创建测试报告...
2025-06-12 14:20:43,949  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:20:43,950  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142043
2025-06-12 14:20:43,951  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142043/superlink_rx_ci_test_debug_report_20250612_142043.xlsx
2025-06-12 14:20:43,953  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142043/superlink_rx_ci_test_debug_report_20250612_142043.xlsx
2025-06-12 14:20:43,954  INFO: === 开始C/I测试 ===
2025-06-12 14:20:43,954  INFO: === 生成C/I测试用例 ===
2025-06-12 14:20:43,954  INFO: C/I测试参数配置:
2025-06-12 14:20:43,954  INFO:   SYM_RATES: [0]
2025-06-12 14:20:43,955  INFO:   S_CODE_EN: [1]
2025-06-12 14:20:43,955  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:20:43,955  INFO:   SNRS: [100]
2025-06-12 14:20:43,955  INFO:   DATA_LEN: [6]
2025-06-12 14:20:43,955  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:20:43,955  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:20:43,955  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:20:43,956  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:20:43,956  INFO: 总测试用例数: 1
2025-06-12 14:20:43,956  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:20:43,956  INFO: 开始配置VSG信号发生器...
2025-06-12 14:20:46,250  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:20:46,253  INFO: === 灵敏度测试完成 ===
2025-06-12 14:20:46,254  INFO: === 清理测试环境 ===
2025-06-12 14:20:46,255  INFO: 设备连接已断开
2025-06-12 14:20:46,255  INFO: === 测试完成 ===
2025-06-12 14:20:46,256  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:20:46,256  INFO: 测试进度：1/1
2025-06-12 14:20:51,257  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:20:51,262  INFO: 测试完成！总共测试耗时：00:00:08
2025-06-12 14:22:34,728  INFO: 测试开始!
2025-06-12 14:22:34,729  INFO: 测试进度：0/1
2025-06-12 14:22:34,730  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:22:34,730  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:22:34,743  INFO: === 初始化测试环境 ===
2025-06-12 14:22:34,743  INFO: 正在初始化设备连接...
2025-06-12 14:22:34,761  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:22:34,827  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:22:34,836  INFO: 正在创建测试报告...
2025-06-12 14:22:34,837  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:22:34,838  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142234
2025-06-12 14:22:34,841  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142234/superlink_rx_ci_test_debug_report_20250612_142234.xlsx
2025-06-12 14:22:34,844  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142234/superlink_rx_ci_test_debug_report_20250612_142234.xlsx
2025-06-12 14:22:34,845  INFO: === 开始C/I测试 ===
2025-06-12 14:22:34,845  INFO: === 生成C/I测试用例 ===
2025-06-12 14:22:34,845  INFO: C/I测试参数配置:
2025-06-12 14:22:34,846  INFO:   SYM_RATES: [0]
2025-06-12 14:22:34,846  INFO:   S_CODE_EN: [1]
2025-06-12 14:22:34,846  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:22:34,846  INFO:   SNRS: [100]
2025-06-12 14:22:34,846  INFO:   DATA_LEN: [6]
2025-06-12 14:22:34,846  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:22:34,846  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:22:34,847  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:22:34,847  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:22:34,847  INFO: 总测试用例数: 1
2025-06-12 14:22:34,847  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:22:34,847  INFO: 开始配置VSG信号发生器...
2025-06-12 14:22:36,945  ERROR: VSG信号发生器配置失败: 'fsk_freq_dev'
2025-06-12 14:22:36,946  INFO: === 灵敏度测试完成 ===
2025-06-12 14:22:36,947  INFO: === 清理测试环境 ===
2025-06-12 14:22:36,948  INFO: 设备连接已断开
2025-06-12 14:22:36,949  INFO: === 测试完成 ===
2025-06-12 14:22:36,949  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:22:36,949  INFO: 测试进度：1/1
2025-06-12 14:22:41,954  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:22:41,958  INFO: 测试完成！总共测试耗时：00:00:08
2025-06-12 14:23:54,804  INFO: 测试开始!
2025-06-12 14:23:54,805  INFO: 测试进度：0/1
2025-06-12 14:23:54,805  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:23:54,806  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:23:54,825  INFO: === 初始化测试环境 ===
2025-06-12 14:23:54,826  INFO: 正在初始化设备连接...
2025-06-12 14:23:54,843  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:23:54,909  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:23:54,918  INFO: 正在创建测试报告...
2025-06-12 14:23:54,918  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:23:54,919  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142354
2025-06-12 14:23:54,920  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142354/superlink_rx_ci_test_debug_report_20250612_142354.xlsx
2025-06-12 14:23:54,920  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142354/superlink_rx_ci_test_debug_report_20250612_142354.xlsx
2025-06-12 14:23:54,921  INFO: === 开始C/I测试 ===
2025-06-12 14:23:54,921  INFO: === 生成C/I测试用例 ===
2025-06-12 14:23:54,924  INFO: C/I测试参数配置:
2025-06-12 14:23:54,925  INFO:   SYM_RATES: [0]
2025-06-12 14:23:54,925  INFO:   S_CODE_EN: [1]
2025-06-12 14:23:54,925  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:23:54,927  INFO:   SNRS: [100]
2025-06-12 14:23:54,927  INFO:   DATA_LEN: [6]
2025-06-12 14:23:54,929  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:23:54,930  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:23:54,931  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:23:54,931  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:23:54,931  INFO: 总测试用例数: 1
2025-06-12 14:23:54,932  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:23:54,932  INFO: 开始配置VSG信号发生器...
2025-06-12 14:23:56,950  ERROR: VSG信号发生器配置失败: 'fsk_freq_dev'
2025-06-12 14:23:56,951  INFO: === 灵敏度测试完成 ===
2025-06-12 14:23:56,953  INFO: === 清理测试环境 ===
2025-06-12 14:23:56,954  INFO: 设备连接已断开
2025-06-12 14:23:56,954  INFO: === 测试完成 ===
2025-06-12 14:23:56,954  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:23:56,955  INFO: 测试进度：1/1
2025-06-12 14:24:01,957  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:24:01,961  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-12 14:24:37,093  INFO: 测试开始!
2025-06-12 14:24:37,095  INFO: 测试进度：0/1
2025-06-12 14:24:37,095  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:24:37,096  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:24:37,112  INFO: === 初始化测试环境 ===
2025-06-12 14:24:37,113  INFO: 正在初始化设备连接...
2025-06-12 14:24:37,129  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:24:37,195  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:24:37,203  INFO: 正在创建测试报告...
2025-06-12 14:24:37,204  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:24:37,205  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142437
2025-06-12 14:24:37,205  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142437/superlink_rx_ci_test_debug_report_20250612_142437.xlsx
2025-06-12 14:24:37,206  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142437/superlink_rx_ci_test_debug_report_20250612_142437.xlsx
2025-06-12 14:24:37,207  INFO: === 开始C/I测试 ===
2025-06-12 14:24:37,207  INFO: === 生成C/I测试用例 ===
2025-06-12 14:24:37,207  INFO: C/I测试参数配置:
2025-06-12 14:24:37,207  INFO:   SYM_RATES: [0]
2025-06-12 14:24:37,211  INFO:   S_CODE_EN: [1]
2025-06-12 14:24:37,211  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:24:37,212  INFO:   SNRS: [100]
2025-06-12 14:24:37,212  INFO:   DATA_LEN: [6]
2025-06-12 14:24:37,212  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:24:37,212  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:24:37,212  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:24:37,212  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:24:37,213  INFO: 总测试用例数: 1
2025-06-12 14:24:37,213  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:24:37,213  INFO: 开始配置VSG信号发生器...
2025-06-12 14:24:39,163  ERROR: VSG信号发生器配置失败: vsg_device_real_time_custom_modulation_set_symmetric_fsk_freq_deviation() got an unexpected keyword argument 'freq'
2025-06-12 14:24:39,163  INFO: === 灵敏度测试完成 ===
2025-06-12 14:24:39,166  INFO: === 清理测试环境 ===
2025-06-12 14:24:39,167  INFO: 设备连接已断开
2025-06-12 14:24:39,168  INFO: === 测试完成 ===
2025-06-12 14:24:39,168  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:24:39,168  INFO: 测试进度：1/1
2025-06-12 14:24:44,168  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:24:44,178  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-12 14:28:54,517  INFO: 测试开始!
2025-06-12 14:28:54,519  INFO: 测试进度：0/1
2025-06-12 14:28:54,520  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:28:54,521  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:28:54,540  INFO: === 初始化测试环境 ===
2025-06-12 14:28:54,540  INFO: 正在初始化设备连接...
2025-06-12 14:28:54,557  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:28:54,624  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:28:54,632  INFO: 正在创建测试报告...
2025-06-12 14:28:54,632  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:28:54,634  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142854
2025-06-12 14:28:54,634  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142854/superlink_rx_ci_test_debug_report_20250612_142854.xlsx
2025-06-12 14:28:54,635  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142854/superlink_rx_ci_test_debug_report_20250612_142854.xlsx
2025-06-12 14:28:54,635  INFO: === 开始C/I测试 ===
2025-06-12 14:28:54,635  INFO: === 生成C/I测试用例 ===
2025-06-12 14:28:54,636  INFO: C/I测试参数配置:
2025-06-12 14:28:54,636  INFO:   SYM_RATES: [0]
2025-06-12 14:28:54,636  INFO:   S_CODE_EN: [1]
2025-06-12 14:28:54,641  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:28:54,641  INFO:   SNRS: [100]
2025-06-12 14:28:54,642  INFO:   DATA_LEN: [6]
2025-06-12 14:28:54,642  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:28:54,642  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:28:54,642  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:28:54,642  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:28:54,642  INFO: 总测试用例数: 1
2025-06-12 14:28:54,643  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:28:54,643  INFO: 开始配置VSG信号发生器...
2025-06-12 14:28:56,589  ERROR: VSG信号发生器配置失败: vsg_device_real_time_custom_modulation_set_symmetric_fsk_freq_deviation() got an unexpected keyword argument 'freq'
2025-06-12 14:28:56,590  INFO: === 灵敏度测试完成 ===
2025-06-12 14:28:56,592  INFO: === 清理测试环境 ===
2025-06-12 14:28:56,592  INFO: 设备连接已断开
2025-06-12 14:28:56,593  INFO: === 测试完成 ===
2025-06-12 14:28:56,593  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:28:56,593  INFO: 测试进度：1/1
2025-06-12 14:29:01,593  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:29:01,598  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-12 14:29:34,769  INFO: 测试开始!
2025-06-12 14:29:34,770  INFO: 测试进度：0/1
2025-06-12 14:29:34,771  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:29:34,771  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:29:34,784  INFO: === 初始化测试环境 ===
2025-06-12 14:29:34,784  INFO: 正在初始化设备连接...
2025-06-12 14:29:34,801  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:29:34,867  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:29:34,877  INFO: 正在创建测试报告...
2025-06-12 14:29:34,877  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:29:34,879  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142934
2025-06-12 14:29:34,880  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142934/superlink_rx_ci_test_debug_report_20250612_142934.xlsx
2025-06-12 14:29:34,881  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_142934/superlink_rx_ci_test_debug_report_20250612_142934.xlsx
2025-06-12 14:29:34,882  INFO: === 开始C/I测试 ===
2025-06-12 14:29:34,882  INFO: === 生成C/I测试用例 ===
2025-06-12 14:29:34,882  INFO: C/I测试参数配置:
2025-06-12 14:29:34,882  INFO:   SYM_RATES: [0]
2025-06-12 14:29:34,882  INFO:   S_CODE_EN: [1]
2025-06-12 14:29:34,882  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:29:34,883  INFO:   SNRS: [100]
2025-06-12 14:29:34,883  INFO:   DATA_LEN: [6]
2025-06-12 14:29:34,883  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:29:34,883  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:29:34,888  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:29:34,888  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:29:34,889  INFO: 总测试用例数: 1
2025-06-12 14:29:34,889  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:29:34,892  INFO: 开始配置VSG信号发生器...
2025-06-12 14:29:38,001  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:29:38,002  INFO: === 灵敏度测试完成 ===
2025-06-12 14:29:38,004  INFO: === 清理测试环境 ===
2025-06-12 14:29:38,005  INFO: 设备连接已断开
2025-06-12 14:29:38,005  INFO: === 测试完成 ===
2025-06-12 14:29:38,005  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:29:38,006  INFO: 测试进度：1/1
2025-06-12 14:29:43,009  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:29:43,012  INFO: 测试完成！总共测试耗时：00:00:08
2025-06-12 14:33:55,649  INFO: 测试开始!
2025-06-12 14:33:55,650  INFO: 测试进度：0/1
2025-06-12 14:33:55,651  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:33:55,651  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:33:55,667  INFO: === 初始化测试环境 ===
2025-06-12 14:33:55,667  INFO: 正在初始化设备连接...
2025-06-12 14:33:55,685  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:33:55,752  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:33:55,761  INFO: 正在创建测试报告...
2025-06-12 14:33:55,762  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:33:55,763  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143355
2025-06-12 14:33:55,764  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143355/superlink_rx_ci_test_debug_report_20250612_143355.xlsx
2025-06-12 14:33:55,764  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143355/superlink_rx_ci_test_debug_report_20250612_143355.xlsx
2025-06-12 14:33:55,764  INFO: === 开始C/I测试 ===
2025-06-12 14:33:55,764  INFO: === 生成C/I测试用例 ===
2025-06-12 14:33:55,765  INFO: C/I测试参数配置:
2025-06-12 14:33:55,765  INFO:   SYM_RATES: [0]
2025-06-12 14:33:55,765  INFO:   S_CODE_EN: [1]
2025-06-12 14:33:55,770  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:33:55,770  INFO:   SNRS: [100]
2025-06-12 14:33:55,770  INFO:   DATA_LEN: [6]
2025-06-12 14:33:55,770  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:33:55,770  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:33:55,771  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:33:55,771  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:33:55,771  INFO: 总测试用例数: 1
2025-06-12 14:33:55,771  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:33:55,771  INFO: 开始配置VSG信号发生器...
2025-06-12 14:33:57,837  ERROR: VSG信号发生器配置失败: vsg_device_real_time_custom_modulation_set_transmission_symbol_rate() got an unexpected keyword argument 'rate'
2025-06-12 14:33:57,838  INFO: === 灵敏度测试完成 ===
2025-06-12 14:33:57,840  INFO: === 清理测试环境 ===
2025-06-12 14:33:57,841  INFO: 设备连接已断开
2025-06-12 14:33:57,841  INFO: === 测试完成 ===
2025-06-12 14:33:57,841  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:33:57,842  INFO: 测试进度：1/1
2025-06-12 14:34:02,846  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:34:02,850  INFO: 测试完成！总共测试耗时：00:00:07
2025-06-12 14:34:39,241  INFO: 测试开始!
2025-06-12 14:34:39,243  INFO: 测试进度：0/1
2025-06-12 14:34:39,243  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:34:39,244  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:34:39,260  INFO: === 初始化测试环境 ===
2025-06-12 14:34:39,261  INFO: 正在初始化设备连接...
2025-06-12 14:34:39,277  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:34:39,343  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:34:39,353  INFO: 正在创建测试报告...
2025-06-12 14:34:39,353  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:34:39,355  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143439
2025-06-12 14:34:39,355  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143439/superlink_rx_ci_test_debug_report_20250612_143439.xlsx
2025-06-12 14:34:39,355  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143439/superlink_rx_ci_test_debug_report_20250612_143439.xlsx
2025-06-12 14:34:39,355  INFO: === 开始C/I测试 ===
2025-06-12 14:34:39,356  INFO: === 生成C/I测试用例 ===
2025-06-12 14:34:39,356  INFO: C/I测试参数配置:
2025-06-12 14:34:39,359  INFO:   SYM_RATES: [0]
2025-06-12 14:34:39,360  INFO:   S_CODE_EN: [1]
2025-06-12 14:34:39,360  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:34:39,361  INFO:   SNRS: [100]
2025-06-12 14:34:39,361  INFO:   DATA_LEN: [6]
2025-06-12 14:34:39,361  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:34:39,361  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:34:39,361  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:34:39,362  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:34:39,362  INFO: 总测试用例数: 1
2025-06-12 14:34:39,362  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:34:39,362  INFO: 开始配置VSG信号发生器...
2025-06-12 14:34:42,751  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:34:42,752  INFO: === 灵敏度测试完成 ===
2025-06-12 14:34:42,755  INFO: === 清理测试环境 ===
2025-06-12 14:34:42,756  INFO: 设备连接已断开
2025-06-12 14:34:42,756  INFO: === 测试完成 ===
2025-06-12 14:34:42,756  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:34:42,757  INFO: 测试进度：1/1
2025-06-12 14:34:47,757  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:34:47,762  INFO: 测试完成！总共测试耗时：00:00:09
2025-06-12 14:37:42,374  INFO: 测试开始!
2025-06-12 14:37:42,377  INFO: 测试进度：0/1
2025-06-12 14:37:42,377  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:37:42,377  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:37:42,400  INFO: === 初始化测试环境 ===
2025-06-12 14:37:42,401  INFO: 正在初始化设备连接...
2025-06-12 14:37:42,418  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:37:42,485  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:37:42,498  INFO: 正在创建测试报告...
2025-06-12 14:37:42,499  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:37:42,499  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143742
2025-06-12 14:37:42,500  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143742/superlink_rx_ci_test_debug_report_20250612_143742.xlsx
2025-06-12 14:37:42,501  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143742/superlink_rx_ci_test_debug_report_20250612_143742.xlsx
2025-06-12 14:37:42,504  INFO: === 开始C/I测试 ===
2025-06-12 14:37:42,505  INFO: === 生成C/I测试用例 ===
2025-06-12 14:37:42,508  INFO: C/I测试参数配置:
2025-06-12 14:37:42,508  INFO:   SYM_RATES: [0]
2025-06-12 14:37:42,509  INFO:   S_CODE_EN: [1]
2025-06-12 14:37:42,509  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:37:42,509  INFO:   SNRS: [100]
2025-06-12 14:37:42,510  INFO:   DATA_LEN: [6]
2025-06-12 14:37:42,510  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:37:42,510  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:37:42,510  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:37:42,511  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:37:42,511  INFO: 总测试用例数: 1
2025-06-12 14:37:42,511  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:37:42,511  INFO: 开始配置VSG信号发生器...
2025-06-12 14:37:46,313  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:37:46,314  INFO: === 灵敏度测试完成 ===
2025-06-12 14:37:46,317  INFO: === 清理测试环境 ===
2025-06-12 14:37:46,317  INFO: 设备连接已断开
2025-06-12 14:37:46,318  INFO: === 测试完成 ===
2025-06-12 14:37:46,318  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:37:46,318  INFO: 测试进度：1/1
2025-06-12 14:37:51,320  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:37:51,325  INFO: 测试完成！总共测试耗时：00:00:09
2025-06-12 14:39:02,684  INFO: 测试开始!
2025-06-12 14:39:02,685  INFO: 测试进度：0/1
2025-06-12 14:39:02,686  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:39:02,687  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:39:02,702  INFO: === 初始化测试环境 ===
2025-06-12 14:39:02,703  INFO: 正在初始化设备连接...
2025-06-12 14:39:02,721  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:39:02,788  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:39:02,798  INFO: 正在创建测试报告...
2025-06-12 14:39:02,798  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:39:02,799  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143902
2025-06-12 14:39:02,801  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143902/superlink_rx_ci_test_debug_report_20250612_143902.xlsx
2025-06-12 14:39:02,801  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_143902/superlink_rx_ci_test_debug_report_20250612_143902.xlsx
2025-06-12 14:39:02,802  INFO: === 开始C/I测试 ===
2025-06-12 14:39:02,802  INFO: === 生成C/I测试用例 ===
2025-06-12 14:39:02,802  INFO: C/I测试参数配置:
2025-06-12 14:39:02,802  INFO:   SYM_RATES: [0]
2025-06-12 14:39:02,803  INFO:   S_CODE_EN: [1]
2025-06-12 14:39:02,803  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:39:02,803  INFO:   SNRS: [100]
2025-06-12 14:39:02,803  INFO:   DATA_LEN: [6]
2025-06-12 14:39:02,803  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:39:02,804  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:39:02,804  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:39:02,804  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:39:02,804  INFO: 总测试用例数: 1
2025-06-12 14:39:02,804  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:39:02,804  INFO: 开始配置VSG信号发生器...
2025-06-12 14:39:07,867  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:39:07,868  INFO: === 灵敏度测试完成 ===
2025-06-12 14:39:07,869  INFO: === 清理测试环境 ===
2025-06-12 14:39:07,870  INFO: 设备连接已断开
2025-06-12 14:39:07,870  INFO: === 测试完成 ===
2025-06-12 14:39:07,871  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:39:07,871  INFO: 测试进度：1/1
2025-06-12 14:39:12,876  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:39:12,880  INFO: 测试完成！总共测试耗时：00:00:10
2025-06-12 14:45:15,148  INFO: 测试开始!
2025-06-12 14:45:15,149  INFO: 测试进度：0/1
2025-06-12 14:45:15,150  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:45:15,151  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:45:15,180  INFO: === 初始化测试环境 ===
2025-06-12 14:45:15,183  INFO: 正在初始化设备连接...
2025-06-12 14:45:15,199  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:45:15,265  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:45:15,282  INFO: 正在创建测试报告...
2025-06-12 14:45:15,283  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:45:15,284  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_144515
2025-06-12 14:45:15,284  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_144515/superlink_rx_ci_test_debug_report_20250612_144515.xlsx
2025-06-12 14:45:15,285  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_144515/superlink_rx_ci_test_debug_report_20250612_144515.xlsx
2025-06-12 14:45:15,286  INFO: === 开始C/I测试 ===
2025-06-12 14:45:15,286  INFO: === 生成C/I测试用例 ===
2025-06-12 14:45:15,288  INFO: C/I测试参数配置:
2025-06-12 14:45:15,292  INFO:   SYM_RATES: [0]
2025-06-12 14:45:15,293  INFO:   S_CODE_EN: [1]
2025-06-12 14:45:15,293  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:45:15,294  INFO:   SNRS: [100]
2025-06-12 14:45:15,294  INFO:   DATA_LEN: [6]
2025-06-12 14:45:15,297  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:45:15,297  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:45:15,298  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:45:15,298  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:45:15,298  INFO: 总测试用例数: 1
2025-06-12 14:45:15,298  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:45:15,298  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 14:45:15,299  INFO: 开始配置CMW信号发生器...
2025-06-12 14:45:16,297  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:45:16,297  INFO: 开始加载ARB文件...
2025-06-12 14:45:21,313  ERROR: 加载ARB文件失败: timed out
2025-06-12 14:45:21,317  ERROR: 加载ARB文件失败: timed out
2025-06-12 14:45:21,356  INFO: === 灵敏度测试完成 ===
2025-06-12 14:45:21,356  INFO: === 清理测试环境 ===
2025-06-12 14:45:21,365  INFO: 设备连接已断开
2025-06-12 14:45:21,365  INFO: === 测试完成 ===
2025-06-12 14:45:21,365  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:45:21,365  INFO: 测试进度：1/1
2025-06-12 14:45:26,368  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:45:26,373  INFO: 测试完成！总共测试耗时：00:00:11
2025-06-12 14:55:22,140  INFO: 测试开始!
2025-06-12 14:55:22,141  INFO: 测试进度：0/1
2025-06-12 14:55:22,142  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:55:22,142  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:55:22,167  INFO: === 初始化测试环境 ===
2025-06-12 14:55:22,168  INFO: 正在初始化设备连接...
2025-06-12 14:55:22,190  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:55:22,285  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:55:22,294  INFO: 正在创建测试报告...
2025-06-12 14:55:22,294  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:55:22,295  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_145522
2025-06-12 14:55:22,296  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_145522/superlink_rx_ci_test_debug_report_20250612_145522.xlsx
2025-06-12 14:55:22,296  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_145522/superlink_rx_ci_test_debug_report_20250612_145522.xlsx
2025-06-12 14:55:22,297  INFO: === 开始C/I测试 ===
2025-06-12 14:55:22,298  INFO: === 生成C/I测试用例 ===
2025-06-12 14:55:22,298  INFO: C/I测试参数配置:
2025-06-12 14:55:22,302  INFO:   SYM_RATES: [0]
2025-06-12 14:55:22,303  INFO:   S_CODE_EN: [1]
2025-06-12 14:55:22,303  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:55:22,304  INFO:   SNRS: [100]
2025-06-12 14:55:22,307  INFO:   DATA_LEN: [6]
2025-06-12 14:55:22,307  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:55:22,308  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:55:22,308  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:55:22,309  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:55:22,309  INFO: 总测试用例数: 1
2025-06-12 14:55:22,309  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:55:22,309  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 14:55:22,309  INFO: 开始配置CMW信号发生器...
2025-06-12 14:55:28,326  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:55:28,326  INFO: 开始加载ARB文件...
2025-06-12 14:55:28,548  INFO: ARB文件加载成功: "D:/sle_stream/test/6byte_Sensitivity/1M/qpsk_1Rs_24Fs_1000fps_PType0_seed0_addsine_10bk_sym100db_grdc.wv"
2025-06-12 14:55:33,758  INFO: 开始配置VSG信号发生器...
2025-06-12 14:55:36,217  ERROR: VSG信号发生器配置失败: vsg_device_real_time_custom_modulation_set_transmission_symbol_rate() got an unexpected keyword argument 'rate'
2025-06-12 14:55:36,221  ERROR: VSG信号发生器配置失败: vsg_device_real_time_custom_modulation_set_transmission_symbol_rate() got an unexpected keyword argument 'rate'
2025-06-12 14:55:36,265  INFO: === 灵敏度测试完成 ===
2025-06-12 14:55:36,266  INFO: === 清理测试环境 ===
2025-06-12 14:55:36,268  INFO: 设备连接已断开
2025-06-12 14:55:36,268  INFO: === 测试完成 ===
2025-06-12 14:55:36,268  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:55:36,269  INFO: 测试进度：1/1
2025-06-12 14:55:41,270  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:55:41,273  INFO: 测试完成！总共测试耗时：00:00:19
2025-06-12 14:57:14,486  INFO: 测试开始!
2025-06-12 14:57:14,487  INFO: 测试进度：0/1
2025-06-12 14:57:14,488  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 14:57:14,490  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 14:57:14,514  INFO: === 初始化测试环境 ===
2025-06-12 14:57:14,517  INFO: 正在初始化设备连接...
2025-06-12 14:57:14,533  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 14:57:14,599  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 14:57:14,608  INFO: 正在创建测试报告...
2025-06-12 14:57:14,608  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 14:57:14,609  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_145714
2025-06-12 14:57:14,610  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_145714/superlink_rx_ci_test_debug_report_20250612_145714.xlsx
2025-06-12 14:57:14,611  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_145714/superlink_rx_ci_test_debug_report_20250612_145714.xlsx
2025-06-12 14:57:14,611  INFO: === 开始C/I测试 ===
2025-06-12 14:57:14,614  INFO: === 生成C/I测试用例 ===
2025-06-12 14:57:14,614  INFO: C/I测试参数配置:
2025-06-12 14:57:14,614  INFO:   SYM_RATES: [0]
2025-06-12 14:57:14,614  INFO:   S_CODE_EN: [1]
2025-06-12 14:57:14,615  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 14:57:14,615  INFO:   SNRS: [100]
2025-06-12 14:57:14,615  INFO:   DATA_LEN: [6]
2025-06-12 14:57:14,615  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 14:57:14,615  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 14:57:14,615  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 14:57:14,615  INFO: 成功生成 1 个C/I测试用例
2025-06-12 14:57:14,616  INFO: 总测试用例数: 1
2025-06-12 14:57:14,616  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 14:57:14,616  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 14:57:14,616  INFO: 开始配置CMW信号发生器...
2025-06-12 14:57:20,642  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:57:20,643  INFO: 开始加载ARB文件...
2025-06-12 14:57:20,863  INFO: ARB文件加载成功: "D:/sle_stream/test/6byte_Sensitivity/1M/qpsk_1Rs_24Fs_1000fps_PType0_seed0_addsine_10bk_sym100db_grdc.wv"
2025-06-12 14:57:21,062  INFO: 开始配置VSG信号发生器...
2025-06-12 14:57:26,144  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 14:57:26,145  INFO: 初始化RF板...
2025-06-12 14:57:27,160  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-12 14:57:30,220  ERROR: 测试失败: 配置参数验证失败: 默认RMS电平必须小于最大RMS电平
2025-06-12 14:57:30,273  INFO: === 灵敏度测试完成 ===
2025-06-12 14:57:30,274  INFO: === 清理测试环境 ===
2025-06-12 14:57:30,276  INFO: 设备连接已断开
2025-06-12 14:57:30,277  INFO: === 测试完成 ===
2025-06-12 14:57:30,277  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 14:57:30,277  INFO: 测试进度：1/1
2025-06-12 14:57:35,279  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 14:57:35,283  INFO: 测试完成！总共测试耗时：00:00:21
2025-06-12 15:02:21,442  INFO: 测试开始!
2025-06-12 15:02:21,442  INFO: 测试进度：0/1
2025-06-12 15:02:21,443  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 15:02:21,443  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 15:02:21,453  INFO: === 初始化测试环境 ===
2025-06-12 15:02:21,454  INFO: 正在初始化设备连接...
2025-06-12 15:02:21,471  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 15:02:21,538  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 15:02:21,547  INFO: 正在创建测试报告...
2025-06-12 15:02:21,547  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 15:02:21,548  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_150221
2025-06-12 15:02:21,549  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_150221/superlink_rx_ci_test_debug_report_20250612_150221.xlsx
2025-06-12 15:02:21,549  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_150221/superlink_rx_ci_test_debug_report_20250612_150221.xlsx
2025-06-12 15:02:21,550  INFO: === 开始C/I测试 ===
2025-06-12 15:02:21,551  INFO: === 生成C/I测试用例 ===
2025-06-12 15:02:21,557  INFO: C/I测试参数配置:
2025-06-12 15:02:21,557  INFO:   SYM_RATES: [0]
2025-06-12 15:02:21,557  INFO:   S_CODE_EN: [1]
2025-06-12 15:02:21,558  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 15:02:21,558  INFO:   SNRS: [100]
2025-06-12 15:02:21,558  INFO:   DATA_LEN: [6]
2025-06-12 15:02:21,558  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 15:02:21,558  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 15:02:21,558  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 15:02:21,558  INFO: 成功生成 1 个C/I测试用例
2025-06-12 15:02:21,559  INFO: 总测试用例数: 1
2025-06-12 15:02:21,559  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 15:02:21,559  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 15:02:21,559  INFO: 开始配置CMW信号发生器...
2025-06-12 15:02:27,633  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-12 15:02:27,634  INFO: 开始加载ARB文件...
2025-06-12 15:02:27,866  INFO: ARB文件加载成功: "D:/sle_stream/test/6byte_Sensitivity/1M/qpsk_1Rs_24Fs_1000fps_PType0_seed0_addsine_10bk_sym100db_grdc.wv"
2025-06-12 15:02:28,073  INFO: 开始配置VSG信号发生器...
2025-06-12 15:02:33,164  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 15:02:33,164  INFO: 初始化RF板...
2025-06-12 15:02:34,183  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-12 15:02:38,276  ERROR: 测试失败: 配置参数验证失败: 默认RMS电平必须小于最大RMS电平
2025-06-12 15:02:38,313  INFO: === 灵敏度测试完成 ===
2025-06-12 15:02:38,313  INFO: === 清理测试环境 ===
2025-06-12 15:02:38,316  INFO: 设备连接已断开
2025-06-12 15:02:38,317  INFO: === 测试完成 ===
2025-06-12 15:02:38,318  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 15:02:38,319  INFO: 测试进度：1/1
2025-06-12 15:02:43,318  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 15:02:43,322  INFO: 测试完成！总共测试耗时：00:00:22
2025-06-12 15:04:21,813  INFO: 测试开始!
2025-06-12 15:04:21,814  INFO: 测试进度：0/1
2025-06-12 15:04:21,815  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-12 15:04:21,815  INFO: 当前执行的用例为：superlink_rx_ci_test_debug
2025-06-12 15:04:21,834  INFO: === 初始化测试环境 ===
2025-06-12 15:04:21,835  INFO: 正在初始化设备连接...
2025-06-12 15:04:21,852  INFO: signal_generator host_id: Agilent Technologies, E4438C, MY42080123, C.04.98
2025-06-12 15:04:21,919  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-12 15:04:21,927  INFO: 正在创建测试报告...
2025-06-12 15:04:21,928  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告
2025-06-12 15:04:21,928  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_150421
2025-06-12 15:04:21,929  INFO: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_150421/superlink_rx_ci_test_debug_report_20250612_150421.xlsx
2025-06-12 15:04:21,930  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0612/蓝牙指标测试用例集_测试报告/superlink_rx_ci_test_debug_report_20250612_150421/superlink_rx_ci_test_debug_report_20250612_150421.xlsx
2025-06-12 15:04:21,930  INFO: === 开始C/I测试 ===
2025-06-12 15:04:21,933  INFO: === 生成C/I测试用例 ===
2025-06-12 15:04:21,933  INFO: C/I测试参数配置:
2025-06-12 15:04:21,935  INFO:   SYM_RATES: [0]
2025-06-12 15:04:21,935  INFO:   S_CODE_EN: [1]
2025-06-12 15:04:21,936  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-12 15:04:21,936  INFO:   SNRS: [100]
2025-06-12 15:04:21,936  INFO:   DATA_LEN: [6]
2025-06-12 15:04:21,936  INFO:   INF_FRE_OFFSET_POINT: [0, 1, 2, 3, 4]
2025-06-12 15:04:21,936  INFO:   INF_FRE_CALC_OP: [1, -1]
2025-06-12 15:04:21,937  INFO: 测试用例数量(54)超过限制(1)，将只执行前1个用例
2025-06-12 15:04:21,937  INFO: 成功生成 1 个C/I测试用例
2025-06-12 15:04:21,937  INFO: 总测试用例数: 1
2025-06-12 15:04:21,937  INFO: 
=== 执行测试用例 1/1 ===
2025-06-12 15:04:21,937  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
2025-06-12 15:04:21,937  INFO: 开始配置CMW信号发生器...
2025-06-12 15:04:27,970  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-12 15:04:27,970  INFO: 开始加载ARB文件...
2025-06-12 15:04:28,200  INFO: ARB文件加载成功: "D:/sle_stream/test/6byte_Sensitivity/1M/qpsk_1Rs_24Fs_1000fps_PType0_seed0_addsine_10bk_sym100db_grdc.wv"
2025-06-12 15:04:28,409  INFO: 开始配置VSG信号发生器...
2025-06-12 15:04:33,371  INFO: VSG信号发生器配置完成，频率: 2402.007MHz
2025-06-12 15:04:33,372  INFO: 初始化RF板...
2025-06-12 15:04:34,386  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-12 15:04:38,478  INFO: ✨ 开始C/I测试数据解析
2025-06-12 15:04:38,478  INFO: 🔧 测试参数: default_rms_level=-90dBm, quick_step=5dB, slow_step=1dB, ref_per=10%
2025-06-12 15:04:38,649  INFO: 🔍 步骤1: 默认干扰信号RMS电平 -90dBm时 并获取初始PER值
2025-06-12 15:04:41,193  INFO: Waiting for initial data collection...
2025-06-12 15:04:46,195  INFO: Collecting first data...
2025-06-12 15:04:47,230  INFO: Starting combined data collection...
2025-06-12 15:04:47,231  INFO: 开始合并数据采集流程...
2025-06-12 15:04:50,300  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:04:50.300', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:04:52,349  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:04:52.349', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:04:52,455  INFO: 正在进行第二次数据收集...
2025-06-12 15:04:53,486  INFO: 继续采集：rx_ok差值(6261)在范围内，继续等待...
2025-06-12 15:04:54,608  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:04:54.607', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:04:56,648  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:04:56.648', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:04:57,472  INFO: 正在进行第二次数据收集...
2025-06-12 15:04:58,522  INFO: 数据采集完成：rx_ok差值(11278) > 10000
2025-06-12 15:04:58,522  INFO: 合并数据采集完成
2025-06-12 15:04:58,527  INFO: Calculating PER...
2025-06-12 15:04:59,572  INFO: 📊 初始测试结果: 干扰信号RMS电平=-90dBm, PER=0.0%
2025-06-12 15:04:59,572  INFO: 🚀 步骤2.1: 开始快速步进阶段
2025-06-12 15:04:59,574  INFO: 🔽 快速步进 1: 设置干扰信号RMS电平为 -85dBm
2025-06-12 15:05:02,313  INFO: Waiting for initial data collection...
2025-06-12 15:05:07,320  INFO: Collecting first data...
2025-06-12 15:05:08,354  INFO: Starting combined data collection...
2025-06-12 15:05:08,354  INFO: 开始合并数据采集流程...
2025-06-12 15:05:11,440  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:05:11.440', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:05:13,482  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:05:13.482', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:05:13,585  INFO: 正在进行第二次数据收集...
2025-06-12 15:05:14,616  INFO: 继续采集：rx_ok差值(6265)在范围内，继续等待...
2025-06-12 15:05:15,740  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:05:15.739', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:05:17,784  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:05:17.783', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:05:18,607  INFO: 正在进行第二次数据收集...
2025-06-12 15:05:19,643  INFO: 数据采集完成：rx_ok差值(11287) > 10000
2025-06-12 15:05:19,643  INFO: 合并数据采集完成
2025-06-12 15:05:19,645  INFO: Calculating PER...
2025-06-12 15:05:21,701  INFO: 📊 快速步进结果: 干扰信号RMS电平=-85dBm, PER=0.0%
2025-06-12 15:05:21,702  INFO: 🔽 快速步进 2: 设置干扰信号RMS电平为 -80dBm
2025-06-12 15:05:24,321  INFO: Waiting for initial data collection...
2025-06-12 15:05:29,324  INFO: Collecting first data...
2025-06-12 15:05:30,357  INFO: Starting combined data collection...
2025-06-12 15:05:30,358  INFO: 开始合并数据采集流程...
2025-06-12 15:05:33,426  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:05:33.425', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:05:35,458  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:05:35.458', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:05:35,562  INFO: 正在进行第二次数据收集...
2025-06-12 15:05:46,595  INFO: 继续采集：rx_ok差值(6238)在范围内，继续等待...
2025-06-12 15:05:56,696  INFO: 正在进行第二次数据收集...
2025-06-12 15:06:19,410  INFO: 数据采集完成：rx_ok差值(27372) > 10000
2025-06-12 15:06:19,411  INFO: 合并数据采集完成
2025-06-12 15:06:19,415  INFO: Calculating PER...
2025-06-12 15:06:31,107  INFO: 📊 快速步进结果: 干扰信号RMS电平=-80dBm, PER=0.0%
2025-06-12 15:06:31,108  INFO: 🔽 快速步进 3: 设置干扰信号RMS电平为 -75dBm
2025-06-12 15:06:33,713  INFO: Waiting for initial data collection...
2025-06-12 15:06:38,715  INFO: Collecting first data...
2025-06-12 15:06:39,748  INFO: Starting combined data collection...
2025-06-12 15:06:39,749  INFO: 开始合并数据采集流程...
2025-06-12 15:06:42,823  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:06:42.822', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:06:44,850  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:06:44.850', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:06:44,954  INFO: 正在进行第二次数据收集...
2025-06-12 15:06:45,989  INFO: 继续采集：rx_ok差值(6240)在范围内，继续等待...
2025-06-12 15:06:47,106  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:06:47.106', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:06:49,152  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:06:49.152', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:06:49,972  INFO: 正在进行第二次数据收集...
2025-06-12 15:06:51,001  INFO: 数据采集完成：rx_ok差值(11258) > 10000
2025-06-12 15:06:51,003  INFO: 合并数据采集完成
2025-06-12 15:06:51,005  INFO: Calculating PER...
2025-06-12 15:06:53,054  INFO: 📊 快速步进结果: 干扰信号RMS电平=-75dBm, PER=0.0%
2025-06-12 15:06:53,055  INFO: 🔽 快速步进 4: 设置干扰信号RMS电平为 -70dBm
2025-06-12 15:06:55,716  INFO: Waiting for initial data collection...
2025-06-12 15:07:00,721  INFO: Collecting first data...
2025-06-12 15:07:01,758  INFO: Starting combined data collection...
2025-06-12 15:07:01,758  INFO: 开始合并数据采集流程...
2025-06-12 15:07:04,841  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:07:04.841', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:07:06,884  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:07:06.883', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:07:06,990  INFO: 正在进行第二次数据收集...
2025-06-12 15:07:08,025  INFO: 继续采集：rx_ok差值(6268)在范围内，继续等待...
2025-06-12 15:07:09,143  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:07:09.143', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:07:11,188  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:07:11.188', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-12 15:07:12,007  INFO: 正在进行第二次数据收集...
2025-06-12 15:07:13,036  INFO: 数据采集完成：rx_ok差值(11285) > 10000
2025-06-12 15:07:13,036  INFO: 合并数据采集完成
2025-06-12 15:07:13,037  INFO: Calculating PER...
2025-06-12 15:07:15,093  INFO: 📊 快速步进结果: 干扰信号RMS电平=-70dBm, PER=0.0%
2025-06-12 15:07:15,093  INFO: 🔽 快速步进 5: 设置干扰信号RMS电平为 -65dBm
2025-06-12 15:07:17,722  INFO: Waiting for initial data collection...
2025-06-12 15:07:22,724  INFO: Collecting first data...
2025-06-12 15:07:23,759  INFO: Starting combined data collection...
2025-06-12 15:07:23,760  INFO: 开始合并数据采集流程...
2025-06-12 15:07:26,827  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:07:26.827', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '7.3242e-04'}
2025-06-12 15:07:55,264  DEBUG: Register data collected: {'timestamp': '2025-06-12 15:07:55.264', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.8828e-04'}
2025-06-12 15:08:05,366  INFO: 正在进行第二次数据收集...
2025-06-12 15:08:36,376  INFO: 数据采集完成：rx_ok差值(42457) > 10000
2025-06-12 15:08:36,377  INFO: 合并数据采集完成
2025-06-12 15:08:36,379  INFO: Calculating PER...
2025-06-12 15:09:17,202  INFO: 📊 快速步进结果: 干扰信号RMS电平=-65dBm, PER=0.43%
2025-06-12 15:09:17,203  INFO: 🔽 快速步进 6: 设置干扰信号RMS电平为 -60dBm
2025-06-12 15:09:50,414  INFO: Waiting for initial data collection...
2025-06-12 15:09:55,418  INFO: Collecting first data...
2025-06-12 15:10:10,539  INFO: Starting combined data collection...
2025-06-12 15:10:10,540  INFO: 开始合并数据采集流程...
2025-06-12 15:10:20,641  INFO: 正在进行第二次数据收集...
2025-06-12 15:10:51,652  INFO: 数据采集完成：rx_ok差值(969) < 1000
2025-06-12 15:10:51,652  INFO: 合并数据采集完成
2025-06-12 15:10:51,654  INFO: Calculating PER...
2025-06-12 15:11:50,701  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-12 15:11:51,721  INFO: 测试状态清理完成
2025-06-12 15:11:51,725  ERROR: 测试失败: 快速步进阶段测试失败: min() arg is an empty sequence
2025-06-12 15:11:51,768  INFO: === 灵敏度测试完成 ===
2025-06-12 15:11:51,768  INFO: === 清理测试环境 ===
2025-06-12 15:11:51,772  INFO: 设备连接已断开
2025-06-12 15:11:51,772  INFO: === 测试完成 ===
2025-06-12 15:11:51,772  INFO: 当前用例 superlink_rx_ci_test_debug 执行完成！
2025-06-12 15:11:51,772  INFO: 测试进度：1/1
2025-06-12 15:11:56,772  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-12 15:11:56,776  INFO: 测试完成！总共测试耗时：00:02:55
