2025-06-09 09:52:45,563  INFO: 测试开始!
2025-06-09 09:52:45,565  INFO: 测试进度：0/1
2025-06-09 09:52:45,565  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:52:45,566  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:52:45,599  INFO: === 初始化测试环境 ===
2025-06-09 09:52:45,599  INFO: 正在初始化设备连接...
2025-06-09 09:52:45,668  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:52:45,676  INFO: 正在创建测试报告...
2025-06-09 09:52:45,677  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:52:45,677  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095245
2025-06-09 09:52:45,679  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095245/superlink_rx_gauss_report_20250609_095245.xlsx
2025-06-09 09:52:45,679  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095245/superlink_rx_gauss_report_20250609_095245.xlsx
2025-06-09 09:52:45,679  INFO: === 开始高斯测试 ===
2025-06-09 09:52:45,682  INFO: === 生成高斯测试用例 ===
2025-06-09 09:52:45,683  INFO: 高斯测试参数配置:
2025-06-09 09:52:45,683  INFO:   SYM_RATES: [0]
2025-06-09 09:52:45,683  INFO:   S_CODE_EN: [1]
2025-06-09 09:52:45,684  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:52:45,684  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:52:45,684  INFO:   DATA_LEN: [6]
2025-06-09 09:52:45,684  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:52:45,685  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:52:45,685  INFO: 总测试用例数: 10
2025-06-09 09:52:45,685  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:52:45,685  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:52:45,686  INFO: 开始配置CMW信号发生器...
2025-06-09 09:52:51,706  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:52:51,707  INFO: 开始加载ARB文件...
2025-06-09 09:52:51,960  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:54:02,385  INFO: 初始化RF板...
2025-06-09 09:54:08,014  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:54:34,585  INFO: 测试停止!
2025-06-09 09:54:38,592  INFO: 测试开始!
2025-06-09 09:54:38,594  INFO: 测试进度：0/1
2025-06-09 09:54:38,594  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:54:38,595  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:54:38,611  INFO: === 初始化测试环境 ===
2025-06-09 09:54:38,612  INFO: 正在初始化设备连接...
2025-06-09 09:54:38,625  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:54:38,632  INFO: 正在创建测试报告...
2025-06-09 09:54:38,635  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:54:38,641  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095438
2025-06-09 09:54:38,641  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095438/superlink_rx_gauss_report_20250609_095438.xlsx
2025-06-09 09:54:38,643  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095438/superlink_rx_gauss_report_20250609_095438.xlsx
2025-06-09 09:54:38,644  INFO: === 开始高斯测试 ===
2025-06-09 09:54:38,644  INFO: === 生成高斯测试用例 ===
2025-06-09 09:54:38,644  INFO: 高斯测试参数配置:
2025-06-09 09:54:38,645  INFO:   SYM_RATES: [0]
2025-06-09 09:54:38,645  INFO:   S_CODE_EN: [1]
2025-06-09 09:54:38,647  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:54:38,649  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:54:38,651  INFO:   DATA_LEN: [6]
2025-06-09 09:54:38,652  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:54:38,652  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:54:38,653  INFO: 总测试用例数: 10
2025-06-09 09:54:38,654  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:54:38,654  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:54:38,656  INFO: 开始配置CMW信号发生器...
2025-06-09 09:54:44,671  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:54:44,672  INFO: 开始加载ARB文件...
2025-06-09 09:54:44,894  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:54:45,091  INFO: 初始化RF板...
2025-06-09 09:54:50,719  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:55:07,539  INFO: 测试停止!
2025-06-09 09:55:24,367  INFO: 测试开始!
2025-06-09 09:55:24,368  INFO: 测试进度：0/1
2025-06-09 09:55:24,369  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:55:24,370  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:55:24,381  INFO: === 初始化测试环境 ===
2025-06-09 09:55:24,383  INFO: 正在初始化设备连接...
2025-06-09 09:55:24,450  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:55:24,455  INFO: 正在创建测试报告...
2025-06-09 09:55:24,456  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:55:24,456  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095524
2025-06-09 09:55:24,457  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095524/superlink_rx_gauss_report_20250609_095524.xlsx
2025-06-09 09:55:24,457  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095524/superlink_rx_gauss_report_20250609_095524.xlsx
2025-06-09 09:55:24,458  INFO: === 开始高斯测试 ===
2025-06-09 09:55:24,458  INFO: === 生成高斯测试用例 ===
2025-06-09 09:55:24,458  INFO: 高斯测试参数配置:
2025-06-09 09:55:24,467  INFO:   SYM_RATES: [0]
2025-06-09 09:55:24,469  INFO:   S_CODE_EN: [1]
2025-06-09 09:55:24,469  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:55:24,470  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:55:24,470  INFO:   DATA_LEN: [6]
2025-06-09 09:55:24,470  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:55:24,471  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:55:24,471  INFO: 总测试用例数: 10
2025-06-09 09:55:24,471  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:55:24,471  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:55:24,471  INFO: 开始配置CMW信号发生器...
2025-06-09 09:55:30,489  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:55:30,490  INFO: 开始加载ARB文件...
2025-06-09 09:55:30,712  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:55:30,909  INFO: 初始化RF板...
2025-06-09 09:55:36,532  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:55:44,757  INFO: 测试停止!
2025-06-09 09:57:30,002  INFO: 测试开始!
2025-06-09 09:57:30,004  INFO: 测试进度：0/1
2025-06-09 09:57:30,004  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:57:30,004  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:57:30,019  INFO: === 初始化测试环境 ===
2025-06-09 09:57:30,020  INFO: 正在初始化设备连接...
2025-06-09 09:57:30,091  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:57:30,100  INFO: 正在创建测试报告...
2025-06-09 09:57:30,101  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:57:30,102  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095730
2025-06-09 09:57:30,103  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095730/superlink_rx_gauss_report_20250609_095730.xlsx
2025-06-09 09:57:30,103  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095730/superlink_rx_gauss_report_20250609_095730.xlsx
2025-06-09 09:57:30,103  INFO: === 开始高斯测试 ===
2025-06-09 09:57:30,104  INFO: === 生成高斯测试用例 ===
2025-06-09 09:57:30,107  INFO: 高斯测试参数配置:
2025-06-09 09:57:30,107  INFO:   SYM_RATES: [0]
2025-06-09 09:57:30,108  INFO:   S_CODE_EN: [1]
2025-06-09 09:57:30,108  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:57:30,109  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:57:30,109  INFO:   DATA_LEN: [6]
2025-06-09 09:57:30,109  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:57:30,109  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:57:30,110  INFO: 总测试用例数: 10
2025-06-09 09:57:30,111  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:57:30,113  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:57:30,113  INFO: 开始配置CMW信号发生器...
2025-06-09 09:57:36,142  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:57:36,143  INFO: 开始加载ARB文件...
2025-06-09 09:57:36,363  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:57:36,563  INFO: 初始化RF板...
2025-06-09 09:57:41,993  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:58:13,047  INFO: 测试停止!
2025-06-09 10:01:49,134  INFO: 测试开始!
2025-06-09 10:01:49,138  INFO: 测试进度：0/1
2025-06-09 10:01:49,139  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 10:01:49,139  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 10:01:49,160  INFO: === 初始化测试环境 ===
2025-06-09 10:01:49,161  INFO: 正在初始化设备连接...
2025-06-09 10:01:49,232  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 10:01:49,240  INFO: 正在创建测试报告...
2025-06-09 10:01:49,241  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 10:01:49,242  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_100149
2025-06-09 10:01:49,243  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_100149/superlink_rx_gauss_report_20250609_100149.xlsx
2025-06-09 10:01:49,243  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_100149/superlink_rx_gauss_report_20250609_100149.xlsx
2025-06-09 10:01:49,247  INFO: === 开始高斯测试 ===
2025-06-09 10:01:49,247  INFO: === 生成高斯测试用例 ===
2025-06-09 10:01:49,248  INFO: 高斯测试参数配置:
2025-06-09 10:01:49,248  INFO:   SYM_RATES: [0]
2025-06-09 10:01:49,248  INFO:   S_CODE_EN: [1]
2025-06-09 10:01:49,248  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 10:01:49,248  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 10:01:49,249  INFO:   DATA_LEN: [6]
2025-06-09 10:01:49,249  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 10:01:49,249  INFO: 成功生成 10 个高斯测试用例
2025-06-09 10:01:49,249  INFO: 总测试用例数: 10
2025-06-09 10:01:49,249  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 10:01:49,249  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:01:49,249  INFO: 开始配置CMW信号发生器...
2025-06-09 10:01:55,276  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:01:55,277  INFO: 开始加载ARB文件...
2025-06-09 10:01:55,506  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 10:01:55,706  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:02:33,307  INFO: 测试成功 - PER: 31.64%, 极性误差: 1.4282e-02~5.1025e-02
2025-06-09 10:02:33,347  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 10:02:33,347  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:02:33,349  INFO: 开始配置CMW信号发生器...
2025-06-09 10:02:39,375  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:02:39,375  INFO: 开始加载ARB文件...
2025-06-09 10:02:39,697  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 10:03:50,111  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:04:20,512  INFO: 测试成功 - PER: 13.28%, 极性误差: 3.5400e-03~8.3008e-03
2025-06-09 10:04:20,542  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 10:04:20,542  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 10:04:20,544  INFO: 开始配置CMW信号发生器...
2025-06-09 10:04:26,561  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:04:26,562  INFO: 开始加载ARB文件...
2025-06-09 10:04:26,784  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 10:05:37,200  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:06:07,626  INFO: 测试成功 - PER: 3.5%, 极性误差: 2.4414e-04~1.9531e-03
2025-06-09 10:06:07,658  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 10:06:07,658  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 10:06:07,659  INFO: 开始配置CMW信号发生器...
2025-06-09 10:06:13,688  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:06:13,689  INFO: 开始加载ARB文件...
2025-06-09 10:06:13,911  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 10:07:24,357  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:07:54,993  INFO: 测试成功 - PER: 0.46%, 极性误差: 1.2207e-04~3.6621e-04
2025-06-09 10:07:55,039  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 10:07:55,039  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 10:07:55,044  INFO: 开始配置CMW信号发生器...
2025-06-09 10:08:01,065  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:08:01,066  INFO: 开始加载ARB文件...
2025-06-09 10:08:01,297  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 10:09:11,709  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:09:42,153  INFO: 测试成功 - PER: 0.02%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 10:09:42,199  INFO: 
=== 执行测试用例 6/10 ===
2025-06-09 10:09:42,199  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-09 10:09:42,201  INFO: 开始配置CMW信号发生器...
2025-06-09 10:09:48,232  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:09:48,234  INFO: 开始加载ARB文件...
2025-06-09 10:09:48,557  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-09 10:10:58,978  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:11:29,558  INFO: 测试成功 - PER: 0.0%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 10:11:29,591  INFO: 
=== 执行测试用例 7/10 ===
2025-06-09 10:11:29,592  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:11:29,593  INFO: 开始配置CMW信号发生器...
2025-06-09 10:11:35,629  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:11:35,629  INFO: 开始加载ARB文件...
2025-06-09 10:11:35,916  ERROR: ARB文件不存在: ARB文件: qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym0db_grdc.wv不在D:/sle_stream/test/6byte_Gauss/1M/R1_polar3_8目录下
2025-06-09 10:11:35,961  INFO: 
=== 执行测试用例 8/10 ===
2025-06-09 10:11:35,962  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:11:35,963  INFO: 开始配置CMW信号发生器...
2025-06-09 10:11:36,979  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:11:36,980  INFO: 开始加载ARB文件...
2025-06-09 10:11:37,204  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-09 10:12:47,598  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 10:13:39,049  INFO: 测试成功 - PER: 20.36%, 极性误差: 1.9775e-02~2.4292e-02
2025-06-09 10:13:39,095  INFO: 
=== 执行测试用例 9/10 ===
2025-06-09 10:13:39,096  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-09 10:13:39,097  INFO: 开始配置CMW信号发生器...
2025-06-09 10:14:06,519  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:14:06,520  INFO: 开始加载ARB文件...
2025-06-09 10:14:15,985  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-09 10:15:23,947  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 10:15:54,273  INFO: 测试成功 - PER: 4.82%, 极性误差: 2.0752e-03~5.0049e-03
2025-06-09 10:15:54,312  INFO: 
=== 执行测试用例 10/10 ===
2025-06-09 10:15:54,312  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-09 10:15:54,313  INFO: 开始配置CMW信号发生器...
2025-06-09 10:16:00,341  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:16:00,341  INFO: 开始加载ARB文件...
2025-06-09 10:16:00,574  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-09 10:17:10,994  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 10:17:41,349  INFO: 测试成功 - PER: 0.97%, 极性误差: 7.3242e-04~2.5635e-03
2025-06-09 10:17:41,389  INFO: === 高斯测试完成 ===
2025-06-09 10:17:41,391  INFO: === 清理测试环境 ===
2025-06-09 10:17:41,392  INFO: 设备连接已断开
2025-06-09 10:17:41,393  INFO: === 测试完成 ===
2025-06-09 10:17:41,393  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-09 10:17:41,393  INFO: 测试进度：1/1
2025-06-09 10:17:46,398  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-09 10:17:46,402  INFO: 测试完成！总共测试耗时：00:15:11
2025-06-09 10:39:02,389  INFO: 测试开始!
2025-06-09 10:39:02,391  INFO: 测试进度：0/2
2025-06-09 10:39:02,392  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 10:39:02,392  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 10:39:02,415  INFO: === 初始化测试环境 ===
2025-06-09 10:39:02,416  INFO: 正在初始化设备连接...
2025-06-09 10:39:02,485  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 10:39:02,494  INFO: 正在创建测试报告...
2025-06-09 10:39:02,495  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 10:39:02,496  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_103902
2025-06-09 10:39:02,497  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_103902/superlink_rx_gauss_report_20250609_103902.xlsx
2025-06-09 10:39:02,502  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_103902/superlink_rx_gauss_report_20250609_103902.xlsx
2025-06-09 10:39:02,503  INFO: === 开始高斯测试 ===
2025-06-09 10:39:02,503  INFO: === 生成高斯测试用例 ===
2025-06-09 10:39:02,505  INFO: 高斯测试参数配置:
2025-06-09 10:39:02,506  INFO:   SYM_RATES: [0]
2025-06-09 10:39:02,506  INFO:   S_CODE_EN: [1]
2025-06-09 10:39:02,506  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 10:39:02,506  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 10:39:02,507  INFO:   DATA_LEN: [6]
2025-06-09 10:39:02,507  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 10:39:02,507  INFO: 成功生成 10 个高斯测试用例
2025-06-09 10:39:02,508  INFO: 总测试用例数: 10
2025-06-09 10:39:02,508  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 10:39:02,508  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:39:02,509  INFO: 开始配置CMW信号发生器...
2025-06-09 10:39:08,577  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:39:08,577  INFO: 开始加载ARB文件...
2025-06-09 10:39:08,809  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 10:40:19,227  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:40:56,699  INFO: 测试成功 - PER: 30.91%, 极性误差: 1.0986e-02~1.8677e-02
2025-06-09 10:40:56,736  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 10:40:56,737  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:40:56,738  INFO: 开始配置CMW信号发生器...
2025-06-09 10:41:02,768  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:41:02,769  INFO: 开始加载ARB文件...
2025-06-09 10:41:03,002  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 10:42:16,077  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:42:25,226  INFO: 测试停止!
2025-06-09 10:43:12,305  INFO: 测试开始!
2025-06-09 10:43:12,306  INFO: 测试进度：0/2
2025-06-09 10:43:12,310  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 10:43:12,310  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 10:43:12,330  INFO: === 初始化测试环境 ===
2025-06-09 10:43:12,330  INFO: 正在初始化设备连接...
2025-06-09 10:43:12,399  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 10:43:12,411  INFO: 正在创建测试报告...
2025-06-09 10:43:12,411  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 10:43:12,412  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_104312
2025-06-09 10:43:12,413  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_104312/superlink_rx_gauss_report_20250609_104312.xlsx
2025-06-09 10:43:12,414  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_104312/superlink_rx_gauss_report_20250609_104312.xlsx
2025-06-09 10:43:12,414  INFO: === 开始高斯测试 ===
2025-06-09 10:43:12,417  INFO: === 生成高斯测试用例 ===
2025-06-09 10:43:12,418  INFO: 高斯测试参数配置:
2025-06-09 10:43:12,418  INFO:   SYM_RATES: [0]
2025-06-09 10:43:12,418  INFO:   S_CODE_EN: [1]
2025-06-09 10:43:12,418  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 10:43:12,419  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 10:43:12,419  INFO:   DATA_LEN: [6]
2025-06-09 10:43:12,419  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 10:43:12,419  INFO: 成功生成 10 个高斯测试用例
2025-06-09 10:43:12,419  INFO: 总测试用例数: 10
2025-06-09 10:43:12,419  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 10:43:12,420  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:43:12,420  INFO: 开始配置CMW信号发生器...
2025-06-09 10:43:18,446  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:43:18,446  INFO: 开始加载ARB文件...
2025-06-09 10:43:18,667  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 10:44:29,077  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:45:04,453  INFO: 测试成功 - PER: 31.17%, 极性误差: 7.8125e-03~2.1118e-02
2025-06-09 10:45:04,491  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 10:45:04,492  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:45:04,493  INFO: 开始配置CMW信号发生器...
2025-06-09 10:45:10,511  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:45:10,512  INFO: 开始加载ARB文件...
2025-06-09 10:45:10,734  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 10:46:21,135  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:46:52,502  INFO: 测试成功 - PER: 12.98%, 极性误差: 4.5166e-03~5.1270e-03
2025-06-09 10:46:52,532  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 10:46:52,532  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 10:46:52,533  INFO: 开始配置CMW信号发生器...
2025-06-09 10:46:58,608  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:46:58,609  INFO: 开始加载ARB文件...
2025-06-09 10:46:58,839  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 10:48:09,244  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:48:38,617  INFO: 测试成功 - PER: 3.39%, 极性误差: 4.8828e-04~2.5635e-03
2025-06-09 10:48:38,648  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 10:48:38,648  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 10:48:38,650  INFO: 开始配置CMW信号发生器...
2025-06-09 10:48:44,675  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:48:44,676  INFO: 开始加载ARB文件...
2025-06-09 10:48:44,908  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 10:50:08,954  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:54:42,265  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 10:54:42,385  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 10:54:42,386  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 10:54:42,387  INFO: 开始配置CMW信号发生器...
2025-06-09 10:56:07,471  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:56:07,472  INFO: 开始加载ARB文件...
2025-06-09 10:56:19,243  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 10:57:36,646  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:00:30,977  INFO: 测试停止!
2025-06-09 11:02:24,832  INFO: 测试开始!
2025-06-09 11:02:24,833  INFO: 测试进度：0/2
2025-06-09 11:02:24,834  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 11:02:24,834  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 11:02:24,863  INFO: === 初始化测试环境 ===
2025-06-09 11:02:24,863  INFO: 正在初始化设备连接...
2025-06-09 11:02:24,932  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 11:02:24,940  INFO: 正在创建测试报告...
2025-06-09 11:02:24,943  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 11:02:24,944  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110224
2025-06-09 11:02:24,946  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110224/superlink_rx_gauss_report_20250609_110224.xlsx
2025-06-09 11:02:24,946  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110224/superlink_rx_gauss_report_20250609_110224.xlsx
2025-06-09 11:02:24,946  INFO: === 开始高斯测试 ===
2025-06-09 11:02:24,947  INFO: === 生成高斯测试用例 ===
2025-06-09 11:02:24,947  INFO: 高斯测试参数配置:
2025-06-09 11:02:24,950  INFO:   SYM_RATES: [0]
2025-06-09 11:02:24,951  INFO:   S_CODE_EN: [1]
2025-06-09 11:02:24,951  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 11:02:24,952  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 11:02:24,952  INFO:   DATA_LEN: [6]
2025-06-09 11:02:24,952  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 11:02:24,952  INFO: 成功生成 10 个高斯测试用例
2025-06-09 11:02:24,952  INFO: 总测试用例数: 10
2025-06-09 11:02:24,953  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 11:02:24,953  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 11:02:24,953  INFO: 开始配置CMW信号发生器...
2025-06-09 11:02:30,968  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:02:30,968  INFO: 开始加载ARB文件...
2025-06-09 11:02:31,188  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 11:03:41,605  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:04:03,324  ERROR: 测试失败: calc gauss per err!
2025-06-09 11:04:03,368  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 11:04:03,368  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 11:04:03,370  INFO: 开始配置CMW信号发生器...
2025-06-09 11:04:09,405  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:04:09,405  INFO: 开始加载ARB文件...
2025-06-09 11:04:09,635  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 11:04:58,418  INFO: 测试停止!
2025-06-09 11:08:30,428  INFO: 测试开始!
2025-06-09 11:08:30,429  INFO: 测试进度：0/2
2025-06-09 11:08:30,430  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 11:08:30,430  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 11:08:30,456  INFO: === 初始化测试环境 ===
2025-06-09 11:08:30,457  INFO: 正在初始化设备连接...
2025-06-09 11:08:30,526  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 11:08:30,537  INFO: 正在创建测试报告...
2025-06-09 11:08:30,538  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 11:08:30,539  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110830
2025-06-09 11:08:30,539  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110830/superlink_rx_gauss_report_20250609_110830.xlsx
2025-06-09 11:08:30,540  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110830/superlink_rx_gauss_report_20250609_110830.xlsx
2025-06-09 11:08:30,542  INFO: === 开始高斯测试 ===
2025-06-09 11:08:30,542  INFO: === 生成高斯测试用例 ===
2025-06-09 11:08:30,543  INFO: 高斯测试参数配置:
2025-06-09 11:08:30,543  INFO:   SYM_RATES: [0]
2025-06-09 11:08:30,544  INFO:   S_CODE_EN: [1]
2025-06-09 11:08:30,544  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 11:08:30,545  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 11:08:30,548  INFO:   DATA_LEN: [6]
2025-06-09 11:08:30,548  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 11:08:30,549  INFO: 成功生成 10 个高斯测试用例
2025-06-09 11:08:30,550  INFO: 总测试用例数: 10
2025-06-09 11:08:30,551  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 11:08:30,551  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 11:08:30,552  INFO: 开始配置CMW信号发生器...
2025-06-09 11:08:36,566  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:08:36,567  INFO: 开始加载ARB文件...
2025-06-09 11:08:36,789  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 11:09:47,193  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:10:21,540  INFO: 测试成功 - PER: 31.45%, 极性误差: 1.1230e-02~1.9043e-02
2025-06-09 11:10:21,573  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 11:10:21,576  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 11:10:21,576  INFO: 开始配置CMW信号发生器...
2025-06-09 11:10:27,604  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:10:27,605  INFO: 开始加载ARB文件...
2025-06-09 11:10:27,827  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 11:12:00,078  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:12:48,587  INFO: 测试成功 - PER: 12.59%, 极性误差: 3.6621e-03~5.9814e-03
2025-06-09 11:12:48,618  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 11:12:48,618  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 11:12:48,620  INFO: 开始配置CMW信号发生器...
2025-06-09 11:12:54,652  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:12:54,652  INFO: 开始加载ARB文件...
2025-06-09 11:12:54,882  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 11:14:05,289  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:14:34,578  INFO: 测试成功 - PER: 3.26%, 极性误差: 1.2207e-03~1.5869e-03
2025-06-09 11:14:34,608  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 11:14:34,609  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 11:14:34,610  INFO: 开始配置CMW信号发生器...
2025-06-09 11:14:40,641  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:14:40,641  INFO: 开始加载ARB文件...
2025-06-09 11:14:40,862  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 11:15:49,150  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:18:07,902  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 11:18:07,960  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 11:18:07,961  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 11:18:07,965  INFO: 开始配置CMW信号发生器...
2025-06-09 11:18:13,984  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:18:13,987  INFO: 开始加载ARB文件...
2025-06-09 11:18:14,203  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 11:19:41,315  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:20:06,046  INFO: 测试停止!
2025-06-09 13:20:21,903  INFO: 测试开始!
2025-06-09 13:20:21,904  INFO: 测试进度：0/2
2025-06-09 13:20:21,905  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:20:21,905  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:20:21,942  INFO: === 初始化测试环境 ===
2025-06-09 13:20:21,942  INFO: 正在初始化设备连接...
2025-06-09 13:20:25,608  INFO: 测试停止!
2025-06-09 13:22:43,913  INFO: 测试开始!
2025-06-09 13:22:43,914  INFO: 测试进度：0/2
2025-06-09 13:22:43,914  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:22:43,915  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:22:43,932  INFO: === 初始化测试环境 ===
2025-06-09 13:22:43,932  INFO: 正在初始化设备连接...
2025-06-09 13:22:44,007  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:22:44,013  INFO: 正在创建测试报告...
2025-06-09 13:22:44,014  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:22:44,015  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132244
2025-06-09 13:22:44,015  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132244/superlink_rx_gauss_report_20250609_132244.xlsx
2025-06-09 13:22:44,016  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132244/superlink_rx_gauss_report_20250609_132244.xlsx
2025-06-09 13:22:44,016  INFO: === 开始高斯测试 ===
2025-06-09 13:22:44,016  INFO: === 生成高斯测试用例 ===
2025-06-09 13:22:44,016  INFO: 高斯测试参数配置:
2025-06-09 13:22:44,016  INFO:   SYM_RATES: [0]
2025-06-09 13:22:44,016  INFO:   S_CODE_EN: [1]
2025-06-09 13:22:44,023  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:22:44,023  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:22:44,024  INFO:   DATA_LEN: [6]
2025-06-09 13:22:44,024  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:22:44,024  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:22:44,026  INFO: 总测试用例数: 10
2025-06-09 13:22:44,027  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:22:44,028  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:22:44,028  INFO: 开始配置CMW信号发生器...
2025-06-09 13:22:45,032  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:22:45,032  INFO: 开始加载ARB文件...
2025-06-09 13:22:45,252  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 13:24:20,176  INFO: 测试停止!
2025-06-09 13:24:44,762  INFO: 测试开始!
2025-06-09 13:24:44,762  INFO: 测试进度：0/2
2025-06-09 13:24:44,763  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:24:44,763  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:24:44,777  INFO: === 初始化测试环境 ===
2025-06-09 13:24:44,778  INFO: 正在初始化设备连接...
2025-06-09 13:24:44,844  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:24:44,852  INFO: 正在创建测试报告...
2025-06-09 13:24:44,852  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:24:44,853  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132444
2025-06-09 13:24:44,855  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132444/superlink_rx_gauss_report_20250609_132444.xlsx
2025-06-09 13:24:44,856  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132444/superlink_rx_gauss_report_20250609_132444.xlsx
2025-06-09 13:24:44,856  INFO: === 开始高斯测试 ===
2025-06-09 13:24:44,856  INFO: === 生成高斯测试用例 ===
2025-06-09 13:24:44,857  INFO: 高斯测试参数配置:
2025-06-09 13:24:44,857  INFO:   SYM_RATES: [0]
2025-06-09 13:24:44,857  INFO:   S_CODE_EN: [1]
2025-06-09 13:24:44,857  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:24:44,857  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:24:44,858  INFO:   DATA_LEN: [6]
2025-06-09 13:24:44,858  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:24:44,858  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:24:44,858  INFO: 总测试用例数: 10
2025-06-09 13:24:44,858  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:24:44,859  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:24:44,859  INFO: 开始配置CMW信号发生器...
2025-06-09 13:24:50,888  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:24:50,889  INFO: 开始加载ARB文件...
2025-06-09 13:24:51,121  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 13:25:02,121  INFO: 测试停止!
