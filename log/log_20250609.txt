2025-06-09 09:52:45,563  INFO: 测试开始!
2025-06-09 09:52:45,565  INFO: 测试进度：0/1
2025-06-09 09:52:45,565  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:52:45,566  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:52:45,599  INFO: === 初始化测试环境 ===
2025-06-09 09:52:45,599  INFO: 正在初始化设备连接...
2025-06-09 09:52:45,668  INFO: bluetooth_tester host_id: <PERSON><PERSON><PERSON><PERSON>,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:52:45,676  INFO: 正在创建测试报告...
2025-06-09 09:52:45,677  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:52:45,677  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095245
2025-06-09 09:52:45,679  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095245/superlink_rx_gauss_report_20250609_095245.xlsx
2025-06-09 09:52:45,679  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095245/superlink_rx_gauss_report_20250609_095245.xlsx
2025-06-09 09:52:45,679  INFO: === 开始高斯测试 ===
2025-06-09 09:52:45,682  INFO: === 生成高斯测试用例 ===
2025-06-09 09:52:45,683  INFO: 高斯测试参数配置:
2025-06-09 09:52:45,683  INFO:   SYM_RATES: [0]
2025-06-09 09:52:45,683  INFO:   S_CODE_EN: [1]
2025-06-09 09:52:45,684  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:52:45,684  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:52:45,684  INFO:   DATA_LEN: [6]
2025-06-09 09:52:45,684  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:52:45,685  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:52:45,685  INFO: 总测试用例数: 10
2025-06-09 09:52:45,685  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:52:45,685  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:52:45,686  INFO: 开始配置CMW信号发生器...
2025-06-09 09:52:51,706  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:52:51,707  INFO: 开始加载ARB文件...
2025-06-09 09:52:51,960  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:54:02,385  INFO: 初始化RF板...
2025-06-09 09:54:08,014  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:54:34,585  INFO: 测试停止!
2025-06-09 09:54:38,592  INFO: 测试开始!
2025-06-09 09:54:38,594  INFO: 测试进度：0/1
2025-06-09 09:54:38,594  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:54:38,595  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:54:38,611  INFO: === 初始化测试环境 ===
2025-06-09 09:54:38,612  INFO: 正在初始化设备连接...
2025-06-09 09:54:38,625  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:54:38,632  INFO: 正在创建测试报告...
2025-06-09 09:54:38,635  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:54:38,641  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095438
2025-06-09 09:54:38,641  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095438/superlink_rx_gauss_report_20250609_095438.xlsx
2025-06-09 09:54:38,643  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095438/superlink_rx_gauss_report_20250609_095438.xlsx
2025-06-09 09:54:38,644  INFO: === 开始高斯测试 ===
2025-06-09 09:54:38,644  INFO: === 生成高斯测试用例 ===
2025-06-09 09:54:38,644  INFO: 高斯测试参数配置:
2025-06-09 09:54:38,645  INFO:   SYM_RATES: [0]
2025-06-09 09:54:38,645  INFO:   S_CODE_EN: [1]
2025-06-09 09:54:38,647  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:54:38,649  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:54:38,651  INFO:   DATA_LEN: [6]
2025-06-09 09:54:38,652  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:54:38,652  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:54:38,653  INFO: 总测试用例数: 10
2025-06-09 09:54:38,654  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:54:38,654  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:54:38,656  INFO: 开始配置CMW信号发生器...
2025-06-09 09:54:44,671  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:54:44,672  INFO: 开始加载ARB文件...
2025-06-09 09:54:44,894  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:54:45,091  INFO: 初始化RF板...
2025-06-09 09:54:50,719  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:55:07,539  INFO: 测试停止!
2025-06-09 09:55:24,367  INFO: 测试开始!
2025-06-09 09:55:24,368  INFO: 测试进度：0/1
2025-06-09 09:55:24,369  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:55:24,370  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:55:24,381  INFO: === 初始化测试环境 ===
2025-06-09 09:55:24,383  INFO: 正在初始化设备连接...
2025-06-09 09:55:24,450  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:55:24,455  INFO: 正在创建测试报告...
2025-06-09 09:55:24,456  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:55:24,456  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095524
2025-06-09 09:55:24,457  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095524/superlink_rx_gauss_report_20250609_095524.xlsx
2025-06-09 09:55:24,457  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095524/superlink_rx_gauss_report_20250609_095524.xlsx
2025-06-09 09:55:24,458  INFO: === 开始高斯测试 ===
2025-06-09 09:55:24,458  INFO: === 生成高斯测试用例 ===
2025-06-09 09:55:24,458  INFO: 高斯测试参数配置:
2025-06-09 09:55:24,467  INFO:   SYM_RATES: [0]
2025-06-09 09:55:24,469  INFO:   S_CODE_EN: [1]
2025-06-09 09:55:24,469  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:55:24,470  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:55:24,470  INFO:   DATA_LEN: [6]
2025-06-09 09:55:24,470  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:55:24,471  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:55:24,471  INFO: 总测试用例数: 10
2025-06-09 09:55:24,471  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:55:24,471  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:55:24,471  INFO: 开始配置CMW信号发生器...
2025-06-09 09:55:30,489  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:55:30,490  INFO: 开始加载ARB文件...
2025-06-09 09:55:30,712  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:55:30,909  INFO: 初始化RF板...
2025-06-09 09:55:36,532  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:55:44,757  INFO: 测试停止!
2025-06-09 09:57:30,002  INFO: 测试开始!
2025-06-09 09:57:30,004  INFO: 测试进度：0/1
2025-06-09 09:57:30,004  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 09:57:30,004  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 09:57:30,019  INFO: === 初始化测试环境 ===
2025-06-09 09:57:30,020  INFO: 正在初始化设备连接...
2025-06-09 09:57:30,091  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 09:57:30,100  INFO: 正在创建测试报告...
2025-06-09 09:57:30,101  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 09:57:30,102  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095730
2025-06-09 09:57:30,103  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095730/superlink_rx_gauss_report_20250609_095730.xlsx
2025-06-09 09:57:30,103  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_095730/superlink_rx_gauss_report_20250609_095730.xlsx
2025-06-09 09:57:30,103  INFO: === 开始高斯测试 ===
2025-06-09 09:57:30,104  INFO: === 生成高斯测试用例 ===
2025-06-09 09:57:30,107  INFO: 高斯测试参数配置:
2025-06-09 09:57:30,107  INFO:   SYM_RATES: [0]
2025-06-09 09:57:30,108  INFO:   S_CODE_EN: [1]
2025-06-09 09:57:30,108  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 09:57:30,109  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 09:57:30,109  INFO:   DATA_LEN: [6]
2025-06-09 09:57:30,109  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 09:57:30,109  INFO: 成功生成 10 个高斯测试用例
2025-06-09 09:57:30,110  INFO: 总测试用例数: 10
2025-06-09 09:57:30,111  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 09:57:30,113  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 09:57:30,113  INFO: 开始配置CMW信号发生器...
2025-06-09 09:57:36,142  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 09:57:36,143  INFO: 开始加载ARB文件...
2025-06-09 09:57:36,363  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 09:57:36,563  INFO: 初始化RF板...
2025-06-09 09:57:41,993  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 09:58:13,047  INFO: 测试停止!
2025-06-09 10:01:49,134  INFO: 测试开始!
2025-06-09 10:01:49,138  INFO: 测试进度：0/1
2025-06-09 10:01:49,139  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 10:01:49,139  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 10:01:49,160  INFO: === 初始化测试环境 ===
2025-06-09 10:01:49,161  INFO: 正在初始化设备连接...
2025-06-09 10:01:49,232  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 10:01:49,240  INFO: 正在创建测试报告...
2025-06-09 10:01:49,241  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 10:01:49,242  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_100149
2025-06-09 10:01:49,243  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_100149/superlink_rx_gauss_report_20250609_100149.xlsx
2025-06-09 10:01:49,243  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_100149/superlink_rx_gauss_report_20250609_100149.xlsx
2025-06-09 10:01:49,247  INFO: === 开始高斯测试 ===
2025-06-09 10:01:49,247  INFO: === 生成高斯测试用例 ===
2025-06-09 10:01:49,248  INFO: 高斯测试参数配置:
2025-06-09 10:01:49,248  INFO:   SYM_RATES: [0]
2025-06-09 10:01:49,248  INFO:   S_CODE_EN: [1]
2025-06-09 10:01:49,248  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 10:01:49,248  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 10:01:49,249  INFO:   DATA_LEN: [6]
2025-06-09 10:01:49,249  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 10:01:49,249  INFO: 成功生成 10 个高斯测试用例
2025-06-09 10:01:49,249  INFO: 总测试用例数: 10
2025-06-09 10:01:49,249  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 10:01:49,249  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:01:49,249  INFO: 开始配置CMW信号发生器...
2025-06-09 10:01:55,276  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:01:55,277  INFO: 开始加载ARB文件...
2025-06-09 10:01:55,506  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 10:01:55,706  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:02:33,307  INFO: 测试成功 - PER: 31.64%, 极性误差: 1.4282e-02~5.1025e-02
2025-06-09 10:02:33,347  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 10:02:33,347  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:02:33,349  INFO: 开始配置CMW信号发生器...
2025-06-09 10:02:39,375  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:02:39,375  INFO: 开始加载ARB文件...
2025-06-09 10:02:39,697  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 10:03:50,111  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:04:20,512  INFO: 测试成功 - PER: 13.28%, 极性误差: 3.5400e-03~8.3008e-03
2025-06-09 10:04:20,542  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 10:04:20,542  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 10:04:20,544  INFO: 开始配置CMW信号发生器...
2025-06-09 10:04:26,561  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:04:26,562  INFO: 开始加载ARB文件...
2025-06-09 10:04:26,784  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 10:05:37,200  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:06:07,626  INFO: 测试成功 - PER: 3.5%, 极性误差: 2.4414e-04~1.9531e-03
2025-06-09 10:06:07,658  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 10:06:07,658  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 10:06:07,659  INFO: 开始配置CMW信号发生器...
2025-06-09 10:06:13,688  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:06:13,689  INFO: 开始加载ARB文件...
2025-06-09 10:06:13,911  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 10:07:24,357  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:07:54,993  INFO: 测试成功 - PER: 0.46%, 极性误差: 1.2207e-04~3.6621e-04
2025-06-09 10:07:55,039  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 10:07:55,039  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 10:07:55,044  INFO: 开始配置CMW信号发生器...
2025-06-09 10:08:01,065  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:08:01,066  INFO: 开始加载ARB文件...
2025-06-09 10:08:01,297  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 10:09:11,709  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:09:42,153  INFO: 测试成功 - PER: 0.02%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 10:09:42,199  INFO: 
=== 执行测试用例 6/10 ===
2025-06-09 10:09:42,199  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-09 10:09:42,201  INFO: 开始配置CMW信号发生器...
2025-06-09 10:09:48,232  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:09:48,234  INFO: 开始加载ARB文件...
2025-06-09 10:09:48,557  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-09 10:10:58,978  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:11:29,558  INFO: 测试成功 - PER: 0.0%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 10:11:29,591  INFO: 
=== 执行测试用例 7/10 ===
2025-06-09 10:11:29,592  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:11:29,593  INFO: 开始配置CMW信号发生器...
2025-06-09 10:11:35,629  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:11:35,629  INFO: 开始加载ARB文件...
2025-06-09 10:11:35,916  ERROR: ARB文件不存在: ARB文件: qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym0db_grdc.wv不在D:/sle_stream/test/6byte_Gauss/1M/R1_polar3_8目录下
2025-06-09 10:11:35,961  INFO: 
=== 执行测试用例 8/10 ===
2025-06-09 10:11:35,962  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:11:35,963  INFO: 开始配置CMW信号发生器...
2025-06-09 10:11:36,979  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:11:36,980  INFO: 开始加载ARB文件...
2025-06-09 10:11:37,204  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-09 10:12:47,598  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 10:13:39,049  INFO: 测试成功 - PER: 20.36%, 极性误差: 1.9775e-02~2.4292e-02
2025-06-09 10:13:39,095  INFO: 
=== 执行测试用例 9/10 ===
2025-06-09 10:13:39,096  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-09 10:13:39,097  INFO: 开始配置CMW信号发生器...
2025-06-09 10:14:06,519  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:14:06,520  INFO: 开始加载ARB文件...
2025-06-09 10:14:15,985  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-09 10:15:23,947  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 10:15:54,273  INFO: 测试成功 - PER: 4.82%, 极性误差: 2.0752e-03~5.0049e-03
2025-06-09 10:15:54,312  INFO: 
=== 执行测试用例 10/10 ===
2025-06-09 10:15:54,312  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 4, 'signal_ch': 0}
2025-06-09 10:15:54,313  INFO: 开始配置CMW信号发生器...
2025-06-09 10:16:00,341  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:16:00,341  INFO: 开始加载ARB文件...
2025-06-09 10:16:00,574  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym3db_grdc.wv"
2025-06-09 10:17:10,994  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 10:17:41,349  INFO: 测试成功 - PER: 0.97%, 极性误差: 7.3242e-04~2.5635e-03
2025-06-09 10:17:41,389  INFO: === 高斯测试完成 ===
2025-06-09 10:17:41,391  INFO: === 清理测试环境 ===
2025-06-09 10:17:41,392  INFO: 设备连接已断开
2025-06-09 10:17:41,393  INFO: === 测试完成 ===
2025-06-09 10:17:41,393  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-06-09 10:17:41,393  INFO: 测试进度：1/1
2025-06-09 10:17:46,398  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-06-09 10:17:46,402  INFO: 测试完成！总共测试耗时：00:15:11
2025-06-09 10:39:02,389  INFO: 测试开始!
2025-06-09 10:39:02,391  INFO: 测试进度：0/2
2025-06-09 10:39:02,392  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 10:39:02,392  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 10:39:02,415  INFO: === 初始化测试环境 ===
2025-06-09 10:39:02,416  INFO: 正在初始化设备连接...
2025-06-09 10:39:02,485  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 10:39:02,494  INFO: 正在创建测试报告...
2025-06-09 10:39:02,495  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 10:39:02,496  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_103902
2025-06-09 10:39:02,497  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_103902/superlink_rx_gauss_report_20250609_103902.xlsx
2025-06-09 10:39:02,502  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_103902/superlink_rx_gauss_report_20250609_103902.xlsx
2025-06-09 10:39:02,503  INFO: === 开始高斯测试 ===
2025-06-09 10:39:02,503  INFO: === 生成高斯测试用例 ===
2025-06-09 10:39:02,505  INFO: 高斯测试参数配置:
2025-06-09 10:39:02,506  INFO:   SYM_RATES: [0]
2025-06-09 10:39:02,506  INFO:   S_CODE_EN: [1]
2025-06-09 10:39:02,506  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 10:39:02,506  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 10:39:02,507  INFO:   DATA_LEN: [6]
2025-06-09 10:39:02,507  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 10:39:02,507  INFO: 成功生成 10 个高斯测试用例
2025-06-09 10:39:02,508  INFO: 总测试用例数: 10
2025-06-09 10:39:02,508  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 10:39:02,508  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:39:02,509  INFO: 开始配置CMW信号发生器...
2025-06-09 10:39:08,577  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:39:08,577  INFO: 开始加载ARB文件...
2025-06-09 10:39:08,809  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 10:40:19,227  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:40:56,699  INFO: 测试成功 - PER: 30.91%, 极性误差: 1.0986e-02~1.8677e-02
2025-06-09 10:40:56,736  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 10:40:56,737  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:40:56,738  INFO: 开始配置CMW信号发生器...
2025-06-09 10:41:02,768  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:41:02,769  INFO: 开始加载ARB文件...
2025-06-09 10:41:03,002  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 10:42:16,077  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:42:25,226  INFO: 测试停止!
2025-06-09 10:43:12,305  INFO: 测试开始!
2025-06-09 10:43:12,306  INFO: 测试进度：0/2
2025-06-09 10:43:12,310  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 10:43:12,310  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 10:43:12,330  INFO: === 初始化测试环境 ===
2025-06-09 10:43:12,330  INFO: 正在初始化设备连接...
2025-06-09 10:43:12,399  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 10:43:12,411  INFO: 正在创建测试报告...
2025-06-09 10:43:12,411  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 10:43:12,412  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_104312
2025-06-09 10:43:12,413  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_104312/superlink_rx_gauss_report_20250609_104312.xlsx
2025-06-09 10:43:12,414  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_104312/superlink_rx_gauss_report_20250609_104312.xlsx
2025-06-09 10:43:12,414  INFO: === 开始高斯测试 ===
2025-06-09 10:43:12,417  INFO: === 生成高斯测试用例 ===
2025-06-09 10:43:12,418  INFO: 高斯测试参数配置:
2025-06-09 10:43:12,418  INFO:   SYM_RATES: [0]
2025-06-09 10:43:12,418  INFO:   S_CODE_EN: [1]
2025-06-09 10:43:12,418  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 10:43:12,419  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 10:43:12,419  INFO:   DATA_LEN: [6]
2025-06-09 10:43:12,419  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 10:43:12,419  INFO: 成功生成 10 个高斯测试用例
2025-06-09 10:43:12,419  INFO: 总测试用例数: 10
2025-06-09 10:43:12,419  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 10:43:12,420  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 10:43:12,420  INFO: 开始配置CMW信号发生器...
2025-06-09 10:43:18,446  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:43:18,446  INFO: 开始加载ARB文件...
2025-06-09 10:43:18,667  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 10:44:29,077  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:45:04,453  INFO: 测试成功 - PER: 31.17%, 极性误差: 7.8125e-03~2.1118e-02
2025-06-09 10:45:04,491  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 10:45:04,492  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 10:45:04,493  INFO: 开始配置CMW信号发生器...
2025-06-09 10:45:10,511  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:45:10,512  INFO: 开始加载ARB文件...
2025-06-09 10:45:10,734  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 10:46:21,135  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:46:52,502  INFO: 测试成功 - PER: 12.98%, 极性误差: 4.5166e-03~5.1270e-03
2025-06-09 10:46:52,532  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 10:46:52,532  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 10:46:52,533  INFO: 开始配置CMW信号发生器...
2025-06-09 10:46:58,608  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:46:58,609  INFO: 开始加载ARB文件...
2025-06-09 10:46:58,839  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 10:48:09,244  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:48:38,617  INFO: 测试成功 - PER: 3.39%, 极性误差: 4.8828e-04~2.5635e-03
2025-06-09 10:48:38,648  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 10:48:38,648  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 10:48:38,650  INFO: 开始配置CMW信号发生器...
2025-06-09 10:48:44,675  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:48:44,676  INFO: 开始加载ARB文件...
2025-06-09 10:48:44,908  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 10:50:08,954  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 10:54:42,265  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 10:54:42,385  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 10:54:42,386  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 10:54:42,387  INFO: 开始配置CMW信号发生器...
2025-06-09 10:56:07,471  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 10:56:07,472  INFO: 开始加载ARB文件...
2025-06-09 10:56:19,243  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 10:57:36,646  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:00:30,977  INFO: 测试停止!
2025-06-09 11:02:24,832  INFO: 测试开始!
2025-06-09 11:02:24,833  INFO: 测试进度：0/2
2025-06-09 11:02:24,834  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 11:02:24,834  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 11:02:24,863  INFO: === 初始化测试环境 ===
2025-06-09 11:02:24,863  INFO: 正在初始化设备连接...
2025-06-09 11:02:24,932  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 11:02:24,940  INFO: 正在创建测试报告...
2025-06-09 11:02:24,943  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 11:02:24,944  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110224
2025-06-09 11:02:24,946  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110224/superlink_rx_gauss_report_20250609_110224.xlsx
2025-06-09 11:02:24,946  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110224/superlink_rx_gauss_report_20250609_110224.xlsx
2025-06-09 11:02:24,946  INFO: === 开始高斯测试 ===
2025-06-09 11:02:24,947  INFO: === 生成高斯测试用例 ===
2025-06-09 11:02:24,947  INFO: 高斯测试参数配置:
2025-06-09 11:02:24,950  INFO:   SYM_RATES: [0]
2025-06-09 11:02:24,951  INFO:   S_CODE_EN: [1]
2025-06-09 11:02:24,951  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 11:02:24,952  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 11:02:24,952  INFO:   DATA_LEN: [6]
2025-06-09 11:02:24,952  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 11:02:24,952  INFO: 成功生成 10 个高斯测试用例
2025-06-09 11:02:24,952  INFO: 总测试用例数: 10
2025-06-09 11:02:24,953  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 11:02:24,953  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 11:02:24,953  INFO: 开始配置CMW信号发生器...
2025-06-09 11:02:30,968  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:02:30,968  INFO: 开始加载ARB文件...
2025-06-09 11:02:31,188  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 11:03:41,605  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:04:03,324  ERROR: 测试失败: calc gauss per err!
2025-06-09 11:04:03,368  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 11:04:03,368  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 11:04:03,370  INFO: 开始配置CMW信号发生器...
2025-06-09 11:04:09,405  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:04:09,405  INFO: 开始加载ARB文件...
2025-06-09 11:04:09,635  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 11:04:58,418  INFO: 测试停止!
2025-06-09 11:08:30,428  INFO: 测试开始!
2025-06-09 11:08:30,429  INFO: 测试进度：0/2
2025-06-09 11:08:30,430  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 11:08:30,430  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 11:08:30,456  INFO: === 初始化测试环境 ===
2025-06-09 11:08:30,457  INFO: 正在初始化设备连接...
2025-06-09 11:08:30,526  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 11:08:30,537  INFO: 正在创建测试报告...
2025-06-09 11:08:30,538  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 11:08:30,539  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110830
2025-06-09 11:08:30,539  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110830/superlink_rx_gauss_report_20250609_110830.xlsx
2025-06-09 11:08:30,540  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_110830/superlink_rx_gauss_report_20250609_110830.xlsx
2025-06-09 11:08:30,542  INFO: === 开始高斯测试 ===
2025-06-09 11:08:30,542  INFO: === 生成高斯测试用例 ===
2025-06-09 11:08:30,543  INFO: 高斯测试参数配置:
2025-06-09 11:08:30,543  INFO:   SYM_RATES: [0]
2025-06-09 11:08:30,544  INFO:   S_CODE_EN: [1]
2025-06-09 11:08:30,544  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 11:08:30,545  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 11:08:30,548  INFO:   DATA_LEN: [6]
2025-06-09 11:08:30,548  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 11:08:30,549  INFO: 成功生成 10 个高斯测试用例
2025-06-09 11:08:30,550  INFO: 总测试用例数: 10
2025-06-09 11:08:30,551  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 11:08:30,551  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 11:08:30,552  INFO: 开始配置CMW信号发生器...
2025-06-09 11:08:36,566  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:08:36,567  INFO: 开始加载ARB文件...
2025-06-09 11:08:36,789  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 11:09:47,193  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:10:21,540  INFO: 测试成功 - PER: 31.45%, 极性误差: 1.1230e-02~1.9043e-02
2025-06-09 11:10:21,573  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 11:10:21,576  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 11:10:21,576  INFO: 开始配置CMW信号发生器...
2025-06-09 11:10:27,604  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:10:27,605  INFO: 开始加载ARB文件...
2025-06-09 11:10:27,827  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 11:12:00,078  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:12:48,587  INFO: 测试成功 - PER: 12.59%, 极性误差: 3.6621e-03~5.9814e-03
2025-06-09 11:12:48,618  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 11:12:48,618  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 11:12:48,620  INFO: 开始配置CMW信号发生器...
2025-06-09 11:12:54,652  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:12:54,652  INFO: 开始加载ARB文件...
2025-06-09 11:12:54,882  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 11:14:05,289  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:14:34,578  INFO: 测试成功 - PER: 3.26%, 极性误差: 1.2207e-03~1.5869e-03
2025-06-09 11:14:34,608  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 11:14:34,609  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 11:14:34,610  INFO: 开始配置CMW信号发生器...
2025-06-09 11:14:40,641  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:14:40,641  INFO: 开始加载ARB文件...
2025-06-09 11:14:40,862  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 11:15:49,150  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:18:07,902  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 11:18:07,960  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 11:18:07,961  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 11:18:07,965  INFO: 开始配置CMW信号发生器...
2025-06-09 11:18:13,984  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 11:18:13,987  INFO: 开始加载ARB文件...
2025-06-09 11:18:14,203  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 11:19:41,315  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 11:20:06,046  INFO: 测试停止!
2025-06-09 13:20:21,903  INFO: 测试开始!
2025-06-09 13:20:21,904  INFO: 测试进度：0/2
2025-06-09 13:20:21,905  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:20:21,905  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:20:21,942  INFO: === 初始化测试环境 ===
2025-06-09 13:20:21,942  INFO: 正在初始化设备连接...
2025-06-09 13:20:25,608  INFO: 测试停止!
2025-06-09 13:22:43,913  INFO: 测试开始!
2025-06-09 13:22:43,914  INFO: 测试进度：0/2
2025-06-09 13:22:43,914  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:22:43,915  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:22:43,932  INFO: === 初始化测试环境 ===
2025-06-09 13:22:43,932  INFO: 正在初始化设备连接...
2025-06-09 13:22:44,007  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:22:44,013  INFO: 正在创建测试报告...
2025-06-09 13:22:44,014  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:22:44,015  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132244
2025-06-09 13:22:44,015  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132244/superlink_rx_gauss_report_20250609_132244.xlsx
2025-06-09 13:22:44,016  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132244/superlink_rx_gauss_report_20250609_132244.xlsx
2025-06-09 13:22:44,016  INFO: === 开始高斯测试 ===
2025-06-09 13:22:44,016  INFO: === 生成高斯测试用例 ===
2025-06-09 13:22:44,016  INFO: 高斯测试参数配置:
2025-06-09 13:22:44,016  INFO:   SYM_RATES: [0]
2025-06-09 13:22:44,016  INFO:   S_CODE_EN: [1]
2025-06-09 13:22:44,023  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:22:44,023  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:22:44,024  INFO:   DATA_LEN: [6]
2025-06-09 13:22:44,024  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:22:44,024  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:22:44,026  INFO: 总测试用例数: 10
2025-06-09 13:22:44,027  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:22:44,028  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:22:44,028  INFO: 开始配置CMW信号发生器...
2025-06-09 13:22:45,032  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:22:45,032  INFO: 开始加载ARB文件...
2025-06-09 13:22:45,252  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 13:24:20,176  INFO: 测试停止!
2025-06-09 13:24:44,762  INFO: 测试开始!
2025-06-09 13:24:44,762  INFO: 测试进度：0/2
2025-06-09 13:24:44,763  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:24:44,763  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:24:44,777  INFO: === 初始化测试环境 ===
2025-06-09 13:24:44,778  INFO: 正在初始化设备连接...
2025-06-09 13:24:44,844  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:24:44,852  INFO: 正在创建测试报告...
2025-06-09 13:24:44,852  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:24:44,853  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132444
2025-06-09 13:24:44,855  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132444/superlink_rx_gauss_report_20250609_132444.xlsx
2025-06-09 13:24:44,856  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132444/superlink_rx_gauss_report_20250609_132444.xlsx
2025-06-09 13:24:44,856  INFO: === 开始高斯测试 ===
2025-06-09 13:24:44,856  INFO: === 生成高斯测试用例 ===
2025-06-09 13:24:44,857  INFO: 高斯测试参数配置:
2025-06-09 13:24:44,857  INFO:   SYM_RATES: [0]
2025-06-09 13:24:44,857  INFO:   S_CODE_EN: [1]
2025-06-09 13:24:44,857  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:24:44,857  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:24:44,858  INFO:   DATA_LEN: [6]
2025-06-09 13:24:44,858  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:24:44,858  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:24:44,858  INFO: 总测试用例数: 10
2025-06-09 13:24:44,858  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:24:44,859  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:24:44,859  INFO: 开始配置CMW信号发生器...
2025-06-09 13:24:50,888  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:24:50,889  INFO: 开始加载ARB文件...
2025-06-09 13:24:51,121  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 13:25:02,121  INFO: 测试停止!
2025-06-09 13:27:55,607  INFO: 测试开始!
2025-06-09 13:27:55,608  INFO: 测试进度：0/1
2025-06-09 13:27:55,610  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:27:55,610  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:27:55,626  INFO: === 初始化测试环境 ===
2025-06-09 13:27:55,628  INFO: 正在初始化设备连接...
2025-06-09 13:27:55,694  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:27:55,702  INFO: 正在创建测试报告...
2025-06-09 13:27:55,704  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:27:55,704  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132755
2025-06-09 13:27:55,705  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132755/superlink_rx_gauss_report_20250609_132755.xlsx
2025-06-09 13:27:55,706  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_132755/superlink_rx_gauss_report_20250609_132755.xlsx
2025-06-09 13:27:55,706  INFO: === 开始高斯测试 ===
2025-06-09 13:27:55,707  INFO: === 生成高斯测试用例 ===
2025-06-09 13:27:55,707  INFO: 高斯测试参数配置:
2025-06-09 13:27:55,707  INFO:   SYM_RATES: [0]
2025-06-09 13:27:55,708  INFO:   S_CODE_EN: [1]
2025-06-09 13:27:55,708  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:27:55,712  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:27:55,714  INFO:   DATA_LEN: [6]
2025-06-09 13:27:55,715  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:27:55,715  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:27:55,716  INFO: 总测试用例数: 10
2025-06-09 13:27:55,716  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:27:55,716  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:27:55,717  INFO: 开始配置CMW信号发生器...
2025-06-09 13:28:01,733  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:28:01,733  INFO: 开始加载ARB文件...
2025-06-09 13:28:01,965  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 13:28:27,480  INFO: 测试停止!
2025-06-09 13:30:00,096  INFO: 测试开始!
2025-06-09 13:30:00,097  INFO: 测试进度：0/1
2025-06-09 13:30:00,099  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:30:00,100  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:30:00,116  INFO: === 初始化测试环境 ===
2025-06-09 13:30:00,117  INFO: 正在初始化设备连接...
2025-06-09 13:30:00,184  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:30:00,193  INFO: 正在创建测试报告...
2025-06-09 13:30:00,194  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:30:00,195  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_133000
2025-06-09 13:30:00,196  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_133000/superlink_rx_gauss_report_20250609_133000.xlsx
2025-06-09 13:30:00,196  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_133000/superlink_rx_gauss_report_20250609_133000.xlsx
2025-06-09 13:30:00,200  INFO: === 开始高斯测试 ===
2025-06-09 13:30:00,200  INFO: === 生成高斯测试用例 ===
2025-06-09 13:30:00,201  INFO: 高斯测试参数配置:
2025-06-09 13:30:00,201  INFO:   SYM_RATES: [0]
2025-06-09 13:30:00,201  INFO:   S_CODE_EN: [1]
2025-06-09 13:30:00,202  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:30:00,202  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:30:00,202  INFO:   DATA_LEN: [6]
2025-06-09 13:30:00,202  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:30:00,202  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:30:00,203  INFO: 总测试用例数: 10
2025-06-09 13:30:00,203  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:30:00,211  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:30:00,212  INFO: 开始配置CMW信号发生器...
2025-06-09 13:30:06,229  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:30:06,231  INFO: 开始加载ARB文件...
2025-06-09 13:30:06,462  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 13:30:22,828  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:22.828', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1475e-02'}
2025-06-09 13:30:24,882  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:24.876', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0498e-02'}
2025-06-09 13:30:27,858  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:27.858', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0620e-02'}
2025-06-09 13:30:29,907  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:29.907', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.5991e-02'}
2025-06-09 13:30:31,954  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:31.953', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.6670e-03'}
2025-06-09 13:30:33,991  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:33.991', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2085e-02'}
2025-06-09 13:30:36,041  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:36.040', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.8687e-02'}
2025-06-09 13:30:38,087  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:38.086', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.4771e-02'}
2025-06-09 13:30:40,141  DEBUG: Register data collected: {'timestamp': '2025-06-09 13:30:40.141', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.6602e-02'}
2025-06-09 13:30:42,196  INFO: 测试成功 - PER: 31.83%, 极性误差: 8.6670e-03~2.8687e-02
2025-06-09 13:30:42,230  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 13:30:42,231  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 13:30:42,234  INFO: 开始配置CMW信号发生器...
2025-06-09 13:30:48,258  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 13:30:48,259  INFO: 开始加载ARB文件...
2025-06-09 13:30:48,574  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 13:33:29,262  INFO: 测试停止!
2025-06-09 13:59:54,582  INFO: 测试开始!
2025-06-09 13:59:54,583  INFO: 测试进度：0/1
2025-06-09 13:59:54,584  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 13:59:54,584  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 13:59:54,618  INFO: === 初始化测试环境 ===
2025-06-09 13:59:54,619  INFO: 正在初始化设备连接...
2025-06-09 13:59:54,692  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 13:59:54,701  INFO: 正在创建测试报告...
2025-06-09 13:59:54,701  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 13:59:54,702  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_135954
2025-06-09 13:59:54,703  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_135954/superlink_rx_gauss_report_20250609_135954.xlsx
2025-06-09 13:59:54,704  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_135954/superlink_rx_gauss_report_20250609_135954.xlsx
2025-06-09 13:59:54,709  INFO: === 开始高斯测试 ===
2025-06-09 13:59:54,710  INFO: === 生成高斯测试用例 ===
2025-06-09 13:59:54,710  INFO: 高斯测试参数配置:
2025-06-09 13:59:54,712  INFO:   SYM_RATES: [0]
2025-06-09 13:59:54,712  INFO:   S_CODE_EN: [1]
2025-06-09 13:59:54,713  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 13:59:54,713  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 13:59:54,714  INFO:   DATA_LEN: [6]
2025-06-09 13:59:54,714  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 13:59:54,714  INFO: 成功生成 10 个高斯测试用例
2025-06-09 13:59:54,714  INFO: 总测试用例数: 10
2025-06-09 13:59:54,714  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 13:59:54,715  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 13:59:54,715  INFO: 开始配置CMW信号发生器...
2025-06-09 14:00:00,746  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:00:00,746  INFO: 开始加载ARB文件...
2025-06-09 14:00:00,978  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 14:01:13,923  ERROR: 测试失败: cannot unpack non-iterable bool object
2025-06-09 14:01:13,971  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 14:01:13,972  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 14:01:13,973  INFO: 开始配置CMW信号发生器...
2025-06-09 14:01:18,026  INFO: 测试停止!
2025-06-09 14:11:47,269  INFO: 测试开始!
2025-06-09 14:11:47,271  INFO: 测试进度：0/1
2025-06-09 14:11:47,271  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 14:11:47,272  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 14:11:47,305  INFO: === 初始化测试环境 ===
2025-06-09 14:11:47,305  INFO: 正在初始化设备连接...
2025-06-09 14:11:47,358  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 14:11:47,366  INFO: 正在创建测试报告...
2025-06-09 14:11:47,366  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 14:11:47,367  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_141147
2025-06-09 14:11:47,368  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_141147/superlink_rx_gauss_report_20250609_141147.xlsx
2025-06-09 14:11:47,368  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_141147/superlink_rx_gauss_report_20250609_141147.xlsx
2025-06-09 14:11:47,369  INFO: === 开始高斯测试 ===
2025-06-09 14:11:47,369  INFO: === 生成高斯测试用例 ===
2025-06-09 14:11:47,373  INFO: 高斯测试参数配置:
2025-06-09 14:11:47,373  INFO:   SYM_RATES: [0]
2025-06-09 14:11:47,374  INFO:   S_CODE_EN: [1]
2025-06-09 14:11:47,374  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 14:11:47,374  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 14:11:47,374  INFO:   DATA_LEN: [6]
2025-06-09 14:11:47,374  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 14:11:47,375  INFO: 成功生成 10 个高斯测试用例
2025-06-09 14:11:47,375  INFO: 总测试用例数: 10
2025-06-09 14:11:47,375  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 14:11:47,375  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 14:11:47,375  INFO: 开始配置CMW信号发生器...
2025-06-09 14:11:48,395  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:11:48,395  INFO: 开始加载ARB文件...
2025-06-09 14:11:48,627  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 14:11:52,430  ERROR: 测试失败: cannot unpack non-iterable bool object
2025-06-09 14:11:52,479  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 14:11:52,480  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 14:11:52,481  INFO: 开始配置CMW信号发生器...
2025-06-09 14:11:54,955  INFO: 测试停止!
2025-06-09 14:17:09,545  INFO: 测试开始!
2025-06-09 14:17:09,546  INFO: 测试进度：0/1
2025-06-09 14:17:09,547  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 14:17:09,548  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 14:17:09,571  INFO: === 初始化测试环境 ===
2025-06-09 14:17:09,571  INFO: 正在初始化设备连接...
2025-06-09 14:17:09,618  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 14:17:09,630  INFO: 正在创建测试报告...
2025-06-09 14:17:09,631  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 14:17:09,632  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_141709
2025-06-09 14:17:09,641  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_141709/superlink_rx_gauss_report_20250609_141709.xlsx
2025-06-09 14:17:09,644  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_141709/superlink_rx_gauss_report_20250609_141709.xlsx
2025-06-09 14:17:09,646  INFO: === 开始高斯测试 ===
2025-06-09 14:17:09,646  INFO: === 生成高斯测试用例 ===
2025-06-09 14:17:09,646  INFO: 高斯测试参数配置:
2025-06-09 14:17:09,649  INFO:   SYM_RATES: [0]
2025-06-09 14:17:09,650  INFO:   S_CODE_EN: [1]
2025-06-09 14:17:09,650  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 14:17:09,650  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 14:17:09,651  INFO:   DATA_LEN: [6]
2025-06-09 14:17:09,651  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 14:17:09,651  INFO: 成功生成 10 个高斯测试用例
2025-06-09 14:17:09,651  INFO: 总测试用例数: 10
2025-06-09 14:17:09,651  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 14:17:09,651  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 14:17:09,652  INFO: 开始配置CMW信号发生器...
2025-06-09 14:17:10,645  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:17:10,646  INFO: 开始加载ARB文件...
2025-06-09 14:17:10,868  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 14:17:15,148  INFO: Waiting for initial data collection...
2025-06-09 14:17:20,191  INFO: Collecting first data...
2025-06-09 14:17:21,221  INFO: Starting register data collection...
2025-06-09 14:17:21,723  INFO: Waiting for second data collection...
2025-06-09 14:17:23,253  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:17:23.253', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0254e-02'}
2025-06-09 14:17:25,297  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:17:25.297', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1230e-02'}
2025-06-09 14:17:27,348  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:17:27.348', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.2773e-03'}
2025-06-09 14:17:28,383  INFO: Calculating PER...
2025-06-09 14:17:29,405  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:17:29.403', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0254e-02'}
2025-06-09 14:17:31,461  INFO: 测试成功 - PER: 30.55%, 极性误差: 9.2773e-03~1.1230e-02
2025-06-09 14:17:31,506  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 14:17:31,507  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 14:17:31,509  INFO: 开始配置CMW信号发生器...
2025-06-09 14:17:37,534  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:17:37,535  INFO: 开始加载ARB文件...
2025-06-09 14:17:37,755  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 14:18:51,201  INFO: Waiting for initial data collection...
2025-06-09 14:18:56,249  INFO: Collecting first data...
2025-06-09 14:18:57,282  INFO: Starting register data collection...
2025-06-09 14:18:57,783  INFO: Waiting for second data collection...
2025-06-09 14:18:59,332  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:18:59.332', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.7842e-03'}
2025-06-09 14:19:01,379  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:19:01.379', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.5166e-03'}
2025-06-09 14:19:03,425  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:19:03.424', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.7656e-03'}
2025-06-09 14:19:04,455  INFO: Calculating PER...
2025-06-09 14:19:05,482  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:19:05.481', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '6.1035e-03'}
2025-06-09 14:19:07,542  INFO: 测试成功 - PER: 12.25%, 极性误差: 3.7842e-03~9.7656e-03
2025-06-09 14:19:07,581  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 14:19:07,582  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 14:19:07,583  INFO: 开始配置CMW信号发生器...
2025-06-09 14:19:13,651  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:19:13,653  INFO: 开始加载ARB文件...
2025-06-09 14:19:13,882  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 14:21:00,394  INFO: Waiting for initial data collection...
2025-06-09 14:21:05,424  INFO: Collecting first data...
2025-06-09 14:21:06,462  INFO: Starting register data collection...
2025-06-09 14:21:06,968  INFO: Waiting for second data collection...
2025-06-09 14:21:08,507  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:21:08.506', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0986e-03'}
2025-06-09 14:21:10,539  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:21:10.539', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.7842e-03'}
2025-06-09 14:21:12,585  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:21:12.584', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.1553e-03'}
2025-06-09 14:21:13,617  INFO: Calculating PER...
2025-06-09 14:21:14,649  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:21:14.647', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.2490e-03'}
2025-06-09 14:21:15,682  INFO: 测试成功 - PER: 3.41%, 极性误差: 1.0986e-03~9.1553e-03
2025-06-09 14:21:15,709  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 14:21:15,710  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 14:21:15,711  INFO: 开始配置CMW信号发生器...
2025-06-09 14:21:21,738  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:21:21,738  INFO: 开始加载ARB文件...
2025-06-09 14:21:22,058  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 14:23:50,082  INFO: Waiting for initial data collection...
2025-06-09 14:23:55,106  INFO: Collecting first data...
2025-06-09 14:23:56,139  INFO: Starting register data collection...
2025-06-09 14:23:56,644  INFO: Waiting for second data collection...
2025-06-09 14:23:58,170  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:23:58.170', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.7656e-04'}
2025-06-09 14:24:00,221  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:24:00.220', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '6.1035e-04'}
2025-06-09 14:24:02,264  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:24:02.263', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 14:24:03,292  INFO: Calculating PER...
2025-06-09 14:24:04,317  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:24:04.317', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-04'}
2025-06-09 14:24:05,352  INFO: 测试成功 - PER: 0.46%, 极性误差: 0.0000e+00~9.7656e-04
2025-06-09 14:24:05,390  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 14:24:05,390  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 14:24:05,392  INFO: 开始配置CMW信号发生器...
2025-06-09 14:24:11,423  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:24:11,424  INFO: 开始加载ARB文件...
2025-06-09 14:24:11,653  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 14:24:14,595  INFO: 测试停止!
2025-06-09 14:50:59,441  INFO: 测试开始!
2025-06-09 14:50:59,444  INFO: 测试进度：0/1
2025-06-09 14:50:59,445  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 14:50:59,445  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 14:50:59,486  INFO: === 初始化测试环境 ===
2025-06-09 14:50:59,486  INFO: 正在初始化设备连接...
2025-06-09 14:50:59,556  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 14:50:59,564  INFO: 正在创建测试报告...
2025-06-09 14:50:59,565  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 14:50:59,566  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_145059
2025-06-09 14:50:59,566  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_145059/superlink_rx_gauss_report_20250609_145059.xlsx
2025-06-09 14:50:59,567  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_145059/superlink_rx_gauss_report_20250609_145059.xlsx
2025-06-09 14:50:59,568  INFO: === 开始高斯测试 ===
2025-06-09 14:50:59,574  INFO: === 生成高斯测试用例 ===
2025-06-09 14:50:59,574  INFO: 高斯测试参数配置:
2025-06-09 14:50:59,575  INFO:   SYM_RATES: [0]
2025-06-09 14:50:59,575  INFO:   S_CODE_EN: [1]
2025-06-09 14:50:59,575  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 14:50:59,576  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 14:50:59,576  INFO:   DATA_LEN: [6]
2025-06-09 14:50:59,580  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 14:50:59,580  INFO: 成功生成 10 个高斯测试用例
2025-06-09 14:50:59,581  INFO: 总测试用例数: 10
2025-06-09 14:50:59,581  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 14:50:59,581  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 14:50:59,581  INFO: 开始配置CMW信号发生器...
2025-06-09 14:51:05,612  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:51:05,613  INFO: 开始加载ARB文件...
2025-06-09 14:51:05,843  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 14:52:19,327  INFO: Waiting for initial data collection...
2025-06-09 14:52:24,425  INFO: Collecting first data...
2025-06-09 14:52:25,456  INFO: Starting combined data collection...
2025-06-09 14:52:25,457  INFO: 开始合并数据采集流程...
2025-06-09 14:52:28,525  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:52:28.525', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.5449e-03'}
2025-06-09 14:52:30,573  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:52:30.572', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.9653e-02'}
2025-06-09 14:52:30,679  INFO: 正在进行第二次数据收集...
2025-06-09 14:52:31,711  INFO: 继续采集：rx_ok差值(3895)在范围内，继续等待...
2025-06-09 14:52:32,828  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:52:32.828', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0010e-02'}
2025-06-09 14:52:34,880  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:52:34.880', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.5879e-02'}
2025-06-09 14:52:35,697  INFO: 正在进行第二次数据收集...
2025-06-09 14:52:36,727  INFO: 继续采集：rx_ok差值(7042)在范围内，继续等待...
2025-06-09 14:52:37,847  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:52:37.847', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '7.6904e-03'}
2025-06-09 14:52:39,880  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:52:39.879', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.8877e-03'}
2025-06-09 14:52:40,701  INFO: 正在进行第二次数据收集...
2025-06-09 14:52:41,739  INFO: 数据采集完成：rx_ok差值(10208) > 10000
2025-06-09 14:52:41,739  INFO: 合并数据采集完成
2025-06-09 14:52:41,741  INFO: Calculating PER...
2025-06-09 14:52:42,768  INFO: 测试成功 - PER: 30.88%, 极性误差: 7.6904e-03~2.5879e-02
2025-06-09 14:52:42,803  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 14:52:42,803  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 14:52:42,806  INFO: 开始配置CMW信号发生器...
2025-06-09 14:52:48,830  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:52:48,831  INFO: 开始加载ARB文件...
2025-06-09 14:52:49,062  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 14:55:36,854  INFO: Waiting for initial data collection...
2025-06-09 14:55:41,926  INFO: Collecting first data...
2025-06-09 14:55:42,960  INFO: Starting combined data collection...
2025-06-09 14:55:42,960  INFO: 开始合并数据采集流程...
2025-06-09 14:55:46,026  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:55:46.025', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.3711e-03'}
2025-06-09 14:55:48,062  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:55:48.062', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.9111e-03'}
2025-06-09 14:55:48,164  INFO: 正在进行第二次数据收集...
2025-06-09 14:55:49,198  INFO: 继续采集：rx_ok差值(5331)在范围内，继续等待...
2025-06-09 14:55:50,313  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:55:50.312', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.0752e-03'}
2025-06-09 14:55:52,359  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:55:52.358', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.6621e-03'}
2025-06-09 14:55:53,178  INFO: 正在进行第二次数据收集...
2025-06-09 14:55:54,209  INFO: 继续采集：rx_ok差值(9664)在范围内，继续等待...
2025-06-09 14:55:55,329  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:55:55.329', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '7.3242e-03'}
2025-06-09 14:55:57,375  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:55:57.375', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2939e-02'}
2025-06-09 14:55:58,196  INFO: 正在进行第二次数据收集...
2025-06-09 14:55:59,234  INFO: 数据采集完成：rx_ok差值(13956) > 10000
2025-06-09 14:55:59,235  INFO: 合并数据采集完成
2025-06-09 14:55:59,237  INFO: Calculating PER...
2025-06-09 14:56:00,271  INFO: 测试成功 - PER: 12.97%, 极性误差: 2.0752e-03~1.2939e-02
2025-06-09 14:56:00,298  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 14:56:00,299  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 14:56:00,300  INFO: 开始配置CMW信号发生器...
2025-06-09 14:56:06,324  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:56:06,325  INFO: 开始加载ARB文件...
2025-06-09 14:56:06,547  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 14:57:38,547  INFO: Waiting for initial data collection...
2025-06-09 14:57:43,550  INFO: Collecting first data...
2025-06-09 14:57:44,585  INFO: Starting combined data collection...
2025-06-09 14:57:44,585  INFO: 开始合并数据采集流程...
2025-06-09 14:57:47,651  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:57:47.651', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.5869e-03'}
2025-06-09 14:57:49,687  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:57:49.687', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.7656e-04'}
2025-06-09 14:57:49,790  INFO: 正在进行第二次数据收集...
2025-06-09 14:57:50,824  INFO: 继续采集：rx_ok差值(6043)在范围内，继续等待...
2025-06-09 14:57:51,940  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:57:51.939', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.8828e-04'}
2025-06-09 14:57:53,971  DEBUG: Register data collected: {'timestamp': '2025-06-09 14:57:53.970', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.5449e-04'}
2025-06-09 14:57:54,889  INFO: 正在进行第二次数据收集...
2025-06-09 14:57:55,920  INFO: 数据采集完成：rx_ok差值(10949) > 10000
2025-06-09 14:57:55,920  INFO: 合并数据采集完成
2025-06-09 14:57:55,922  INFO: Calculating PER...
2025-06-09 14:57:56,953  INFO: 测试成功 - PER: 3.36%, 极性误差: 4.8828e-04~1.5869e-03
2025-06-09 14:57:56,989  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 14:57:56,990  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 14:57:56,991  INFO: 开始配置CMW信号发生器...
2025-06-09 14:58:03,021  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 14:58:03,022  INFO: 开始加载ARB文件...
2025-06-09 14:58:03,241  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 15:00:26,406  INFO: Waiting for initial data collection...
2025-06-09 15:00:36,509  INFO: Collecting first data...
2025-06-09 15:00:49,134  INFO: Starting combined data collection...
2025-06-09 15:00:49,135  INFO: 开始合并数据采集流程...
2025-06-09 15:01:01,011  INFO: 正在进行第二次数据收集...
2025-06-09 15:01:12,090  INFO: 数据采集完成：rx_ok差值(24379) > 10000
2025-06-09 15:01:12,091  INFO: 合并数据采集完成
2025-06-09 15:01:12,094  INFO: Calculating PER...
2025-06-09 15:01:27,260  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 15:01:27,261  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 15:01:27,315  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 15:01:27,315  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 15:01:27,317  INFO: 开始配置CMW信号发生器...
2025-06-09 15:01:50,879  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:01:50,880  INFO: 开始加载ARB文件...
2025-06-09 15:01:51,113  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 15:01:52,978  INFO: 测试停止!
2025-06-09 15:15:01,394  INFO: 测试开始!
2025-06-09 15:15:01,396  INFO: 测试进度：0/1
2025-06-09 15:15:01,397  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 15:15:01,398  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 15:15:01,424  INFO: === 初始化测试环境 ===
2025-06-09 15:15:01,426  INFO: 正在初始化设备连接...
2025-06-09 15:15:01,495  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 15:15:01,504  INFO: 正在创建测试报告...
2025-06-09 15:15:01,505  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 15:15:01,506  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_151501
2025-06-09 15:15:01,507  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_151501/superlink_rx_gauss_report_20250609_151501.xlsx
2025-06-09 15:15:01,507  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_151501/superlink_rx_gauss_report_20250609_151501.xlsx
2025-06-09 15:15:01,508  INFO: === 开始高斯测试 ===
2025-06-09 15:15:01,509  INFO: === 生成高斯测试用例 ===
2025-06-09 15:15:01,512  INFO: 高斯测试参数配置:
2025-06-09 15:15:01,513  INFO:   SYM_RATES: [0]
2025-06-09 15:15:01,513  INFO:   S_CODE_EN: [1]
2025-06-09 15:15:01,513  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 15:15:01,513  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 15:15:01,514  INFO:   DATA_LEN: [6]
2025-06-09 15:15:01,514  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 15:15:01,514  INFO: 成功生成 10 个高斯测试用例
2025-06-09 15:15:01,515  INFO: 总测试用例数: 10
2025-06-09 15:15:01,515  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 15:15:01,515  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 15:15:01,516  INFO: 开始配置CMW信号发生器...
2025-06-09 15:15:07,535  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:15:07,536  INFO: 开始加载ARB文件...
2025-06-09 15:15:07,777  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 15:16:47,149  INFO: Waiting for initial data collection...
2025-06-09 15:16:52,151  INFO: Collecting first data...
2025-06-09 15:16:53,186  INFO: Starting combined data collection...
2025-06-09 15:16:53,186  INFO: 开始合并数据采集流程...
2025-06-09 15:16:56,255  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:16:56.254', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.7456e-02'}
2025-06-09 15:16:58,288  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:16:58.288', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.9409e-02'}
2025-06-09 15:16:58,389  INFO: 正在进行第二次数据收集...
2025-06-09 15:16:59,422  INFO: 继续采集：rx_ok差值(3973)在范围内，继续等待...
2025-06-09 15:17:00,540  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:17:00.540', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.7891e-03'}
2025-06-09 15:17:02,575  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:17:02.574', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.7822e-02'}
2025-06-09 15:17:03,493  INFO: 正在进行第二次数据收集...
2025-06-09 15:17:04,523  INFO: 继续采集：rx_ok差值(7146)在范围内，继续等待...
2025-06-09 15:17:05,646  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:17:05.646', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1719e-02'}
2025-06-09 15:17:07,690  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:17:07.689', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.9419e-02'}
2025-06-09 15:17:08,508  INFO: 正在进行第二次数据收集...
2025-06-09 15:17:09,543  INFO: 数据采集完成：rx_ok差值(10194) > 10000
2025-06-09 15:17:09,545  INFO: 合并数据采集完成
2025-06-09 15:17:09,547  INFO: Calculating PER...
2025-06-09 15:17:10,574  INFO: 测试成功 - PER: 31.37%, 极性误差: 8.7891e-03~2.9419e-02
2025-06-09 15:17:10,620  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 15:17:10,620  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 15:17:10,622  INFO: 开始配置CMW信号发生器...
2025-06-09 15:17:16,640  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:17:16,641  INFO: 开始加载ARB文件...
2025-06-09 15:17:16,863  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 15:18:30,323  INFO: Waiting for initial data collection...
2025-06-09 15:18:35,326  INFO: Collecting first data...
2025-06-09 15:18:36,358  INFO: Starting combined data collection...
2025-06-09 15:18:36,359  INFO: 开始合并数据采集流程...
2025-06-09 15:18:39,428  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:18:39.428', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.2959e-03'}
2025-06-09 15:18:41,504  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:18:41.504', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.4893e-02'}
2025-06-09 15:18:41,609  INFO: 正在进行第二次数据收集...
2025-06-09 15:18:42,644  INFO: 继续采集：rx_ok差值(5450)在范围内，继续等待...
2025-06-09 15:18:43,763  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:18:43.763', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1963e-02'}
2025-06-09 15:18:45,813  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:18:45.813', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.1973e-03'}
2025-06-09 15:18:46,642  INFO: 正在进行第二次数据收集...
2025-06-09 15:18:47,676  INFO: 继续采集：rx_ok差值(9788)在范围内，继续等待...
2025-06-09 15:18:48,796  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:18:48.796', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.6855e-03'}
2025-06-09 15:18:50,843  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:18:50.843', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.1504e-03'}
2025-06-09 15:18:51,652  INFO: 正在进行第二次数据收集...
2025-06-09 15:18:52,686  INFO: 数据采集完成：rx_ok差值(14101) > 10000
2025-06-09 15:18:52,686  INFO: 合并数据采集完成
2025-06-09 15:18:52,687  INFO: Calculating PER...
2025-06-09 15:18:53,719  INFO: 测试成功 - PER: 12.46%, 极性误差: 2.1973e-03~1.4893e-02
2025-06-09 15:18:53,745  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 15:18:53,745  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 15:18:53,747  INFO: 开始配置CMW信号发生器...
2025-06-09 15:18:59,768  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:18:59,768  INFO: 开始加载ARB文件...
2025-06-09 15:18:59,988  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 15:21:30,148  INFO: Waiting for initial data collection...
2025-06-09 15:21:35,149  INFO: Collecting first data...
2025-06-09 15:21:36,181  INFO: Starting combined data collection...
2025-06-09 15:21:36,182  INFO: 开始合并数据采集流程...
2025-06-09 15:21:39,255  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:21:39.255', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.5449e-04'}
2025-06-09 15:21:41,299  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:21:41.299', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-03'}
2025-06-09 15:21:41,405  INFO: 正在进行第二次数据收集...
2025-06-09 15:21:42,440  INFO: 继续采集：rx_ok差值(6019)在范围内，继续等待...
2025-06-09 15:21:43,556  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:21:43.556', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '7.3242e-04'}
2025-06-09 15:21:45,590  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:21:45.590', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.8828e-04'}
2025-06-09 15:21:46,407  INFO: 正在进行第二次数据收集...
2025-06-09 15:21:47,436  INFO: 数据采集完成：rx_ok差值(10871) > 10000
2025-06-09 15:21:47,438  INFO: 合并数据采集完成
2025-06-09 15:21:47,439  INFO: Calculating PER...
2025-06-09 15:21:48,469  INFO: 测试成功 - PER: 3.33%, 极性误差: 4.8828e-04~1.2207e-03
2025-06-09 15:21:48,498  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 15:21:48,498  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 15:21:48,500  INFO: 开始配置CMW信号发生器...
2025-06-09 15:21:54,534  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:21:54,536  INFO: 开始加载ARB文件...
2025-06-09 15:21:54,763  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 15:24:26,141  INFO: Waiting for initial data collection...
2025-06-09 15:24:31,140  INFO: Collecting first data...
2025-06-09 15:24:54,898  INFO: Starting combined data collection...
2025-06-09 15:24:54,898  INFO: 开始合并数据采集流程...
2025-06-09 15:25:04,999  INFO: 正在进行第二次数据收集...
2025-06-09 15:25:32,416  INFO: 数据采集完成：rx_ok差值(33712) > 10000
2025-06-09 15:25:32,417  INFO: 合并数据采集完成
2025-06-09 15:25:32,419  INFO: Calculating PER...
2025-06-09 15:25:42,152  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 15:25:42,152  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 15:25:42,188  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 15:25:42,188  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 15:25:42,190  INFO: 开始配置CMW信号发生器...
2025-06-09 15:26:14,605  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:26:14,605  INFO: 开始加载ARB文件...
2025-06-09 15:26:21,258  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 15:26:46,538  INFO: 测试停止!
2025-06-09 15:30:47,891  INFO: 测试开始!
2025-06-09 15:30:47,898  INFO: 测试进度：0/1
2025-06-09 15:30:47,899  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 15:30:47,899  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 15:30:47,936  INFO: === 初始化测试环境 ===
2025-06-09 15:30:47,937  INFO: 正在初始化设备连接...
2025-06-09 15:30:48,007  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 15:30:48,016  INFO: 正在创建测试报告...
2025-06-09 15:30:48,016  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 15:30:48,017  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_153048
2025-06-09 15:30:48,018  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_153048/superlink_rx_gauss_report_20250609_153048.xlsx
2025-06-09 15:30:48,019  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_153048/superlink_rx_gauss_report_20250609_153048.xlsx
2025-06-09 15:30:48,019  INFO: === 开始高斯测试 ===
2025-06-09 15:30:48,020  INFO: === 生成高斯测试用例 ===
2025-06-09 15:30:48,020  INFO: 高斯测试参数配置:
2025-06-09 15:30:48,024  INFO:   SYM_RATES: [0]
2025-06-09 15:30:48,025  INFO:   S_CODE_EN: [1]
2025-06-09 15:30:48,025  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 15:30:48,026  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 15:30:48,026  INFO:   DATA_LEN: [6]
2025-06-09 15:30:48,026  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 15:30:48,026  INFO: 成功生成 10 个高斯测试用例
2025-06-09 15:30:48,027  INFO: 总测试用例数: 10
2025-06-09 15:30:48,027  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 15:30:48,027  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 15:30:48,027  INFO: 开始配置CMW信号发生器...
2025-06-09 15:30:54,085  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:30:54,086  INFO: 开始加载ARB文件...
2025-06-09 15:30:54,318  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 15:32:06,779  INFO: Waiting for initial data collection...
2025-06-09 15:32:11,783  INFO: Collecting first data...
2025-06-09 15:32:12,813  INFO: Starting combined data collection...
2025-06-09 15:32:12,814  INFO: 开始合并数据采集流程...
2025-06-09 15:32:15,885  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:32:15.885', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0620e-02'}
2025-06-09 15:32:17,936  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:32:17.936', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0376e-02'}
2025-06-09 15:32:18,040  INFO: 正在进行第二次数据收集...
2025-06-09 15:32:19,073  INFO: 继续采集：rx_ok差值(3837)在范围内，继续等待...
2025-06-09 15:32:20,194  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:32:20.193', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.1128e-02'}
2025-06-09 15:32:22,239  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:32:22.238', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.6724e-02'}
2025-06-09 15:32:23,046  INFO: 正在进行第二次数据收集...
2025-06-09 15:32:24,081  INFO: 继续采集：rx_ok差值(6852)在范围内，继续等待...
2025-06-09 15:32:25,196  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:32:25.196', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.3184e-02'}
2025-06-09 15:32:27,245  DEBUG: Register data collected: {'timestamp': '2025-06-09 15:32:27.245', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1353e-02'}
2025-06-09 15:32:28,062  INFO: 正在进行第二次数据收集...
2025-06-09 15:32:29,095  INFO: 数据采集完成：rx_ok差值(10051) > 10000
2025-06-09 15:32:29,096  INFO: 合并数据采集完成
2025-06-09 15:32:29,097  INFO: Calculating PER...
2025-06-09 15:32:30,125  INFO: 测试成功 - PER: 31.83%, 极性误差: 1.0376e-02~3.1128e-02
2025-06-09 15:32:30,167  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 15:32:30,168  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 15:32:30,169  INFO: 开始配置CMW信号发生器...
2025-06-09 15:32:36,211  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:32:36,212  INFO: 开始加载ARB文件...
2025-06-09 15:32:36,443  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 15:34:58,833  INFO: Waiting for initial data collection...
2025-06-09 15:35:13,835  INFO: Collecting first data...
2025-06-09 15:35:44,845  INFO: Starting combined data collection...
2025-06-09 15:35:44,846  INFO: 开始合并数据采集流程...
2025-06-09 15:35:54,947  INFO: 正在进行第二次数据收集...
2025-06-09 15:36:25,958  INFO: 数据采集完成：rx_ok差值(35391) > 10000
2025-06-09 15:36:25,958  INFO: 合并数据采集完成
2025-06-09 15:36:25,959  INFO: Calculating PER...
2025-06-09 15:36:56,968  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 15:36:56,969  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 15:36:57,012  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 15:36:57,013  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 15:36:57,014  INFO: 开始配置CMW信号发生器...
2025-06-09 15:37:44,733  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 15:37:44,734  INFO: 开始加载ARB文件...
2025-06-09 15:37:44,956  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 15:37:46,660  INFO: 测试停止!
2025-06-09 16:02:25,374  INFO: 测试开始!
2025-06-09 16:02:25,375  INFO: 测试进度：0/1
2025-06-09 16:02:25,376  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 16:02:25,376  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 16:02:25,397  INFO: === 初始化测试环境 ===
2025-06-09 16:02:25,399  INFO: 正在初始化设备连接...
2025-06-09 16:02:25,466  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 16:02:25,476  INFO: 正在创建测试报告...
2025-06-09 16:02:25,477  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 16:02:25,478  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_160225
2025-06-09 16:02:25,478  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_160225/superlink_rx_gauss_report_20250609_160225.xlsx
2025-06-09 16:02:25,479  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_160225/superlink_rx_gauss_report_20250609_160225.xlsx
2025-06-09 16:02:25,479  INFO: === 开始高斯测试 ===
2025-06-09 16:02:25,482  INFO: === 生成高斯测试用例 ===
2025-06-09 16:02:25,487  INFO: 高斯测试参数配置:
2025-06-09 16:02:25,487  INFO:   SYM_RATES: [0]
2025-06-09 16:02:25,488  INFO:   S_CODE_EN: [1]
2025-06-09 16:02:25,488  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 16:02:25,488  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 16:02:25,489  INFO:   DATA_LEN: [6]
2025-06-09 16:02:25,489  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 16:02:25,489  INFO: 成功生成 10 个高斯测试用例
2025-06-09 16:02:25,490  INFO: 总测试用例数: 10
2025-06-09 16:02:25,490  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 16:02:25,490  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 16:02:25,490  INFO: 开始配置CMW信号发生器...
2025-06-09 16:02:31,518  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:02:31,519  INFO: 开始加载ARB文件...
2025-06-09 16:02:31,761  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 16:03:44,223  INFO: Waiting for initial data collection...
2025-06-09 16:03:49,230  INFO: Collecting first data...
2025-06-09 16:03:50,264  INFO: Starting combined data collection...
2025-06-09 16:03:50,264  INFO: 开始合并数据采集流程...
2025-06-09 16:03:53,367  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:03:53.366', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1841e-02'}
2025-06-09 16:03:55,412  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:03:55.412', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.4229e-03'}
2025-06-09 16:03:55,519  INFO: 正在进行第二次数据收集...
2025-06-09 16:03:56,554  INFO: 继续采集：rx_ok差值(3923)在范围内，继续等待...
2025-06-09 16:03:57,672  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:03:57.672', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.0020e-02'}
2025-06-09 16:03:59,735  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:03:59.735', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.8433e-02'}
2025-06-09 16:04:00,551  INFO: 正在进行第二次数据收集...
2025-06-09 16:04:01,586  INFO: 继续采集：rx_ok差值(6935)在范围内，继续等待...
2025-06-09 16:04:02,706  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:04:02.706', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1963e-02'}
2025-06-09 16:04:04,757  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:04:04.756', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.0332e-03'}
2025-06-09 16:04:05,585  INFO: 正在进行第二次数据收集...
2025-06-09 16:04:06,618  INFO: 数据采集完成：rx_ok差值(10128) > 10000
2025-06-09 16:04:06,620  INFO: 合并数据采集完成
2025-06-09 16:04:06,622  INFO: Calculating PER...
2025-06-09 16:04:08,679  INFO: 测试成功 - PER: 31.64%, 极性误差: 8.4229e-03~2.0020e-02
2025-06-09 16:04:08,718  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 16:04:08,719  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 16:04:08,720  INFO: 开始配置CMW信号发生器...
2025-06-09 16:04:14,746  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:04:14,748  INFO: 开始加载ARB文件...
2025-06-09 16:04:14,979  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 16:05:27,448  INFO: Waiting for initial data collection...
2025-06-09 16:05:32,452  INFO: Collecting first data...
2025-06-09 16:05:33,487  INFO: Starting combined data collection...
2025-06-09 16:05:33,488  INFO: 开始合并数据采集流程...
2025-06-09 16:05:36,572  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:05:36.572', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.9297e-03'}
2025-06-09 16:05:38,621  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:05:38.621', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.0283e-03'}
2025-06-09 16:05:38,727  INFO: 正在进行第二次数据收集...
2025-06-09 16:05:39,756  INFO: 继续采集：rx_ok差值(5379)在范围内，继续等待...
2025-06-09 16:05:40,879  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:05:40.879', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.4932e-03'}
2025-06-09 16:05:42,925  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:05:42.925', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '9.1553e-03'}
2025-06-09 16:05:43,757  INFO: 正在进行第二次数据收集...
2025-06-09 16:05:44,793  INFO: 继续采集：rx_ok差值(9697)在范围内，继续等待...
2025-06-09 16:05:45,905  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:05:45.905', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.0518e-03'}
2025-06-09 16:05:47,942  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:05:47.941', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.0752e-03'}
2025-06-09 16:05:48,770  INFO: 正在进行第二次数据收集...
2025-06-09 16:05:49,805  INFO: 数据采集完成：rx_ok差值(14017) > 10000
2025-06-09 16:05:49,805  INFO: 合并数据采集完成
2025-06-09 16:05:49,807  INFO: Calculating PER...
2025-06-09 16:05:50,838  INFO: 测试成功 - PER: 12.93%, 极性误差: 2.0752e-03~9.1553e-03
2025-06-09 16:05:50,866  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 16:05:50,866  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 16:05:50,867  INFO: 开始配置CMW信号发生器...
2025-06-09 16:05:56,903  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:05:56,904  INFO: 开始加载ARB文件...
2025-06-09 16:05:57,135  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 16:08:19,470  INFO: Waiting for initial data collection...
2025-06-09 16:08:34,472  INFO: Collecting first data...
2025-06-09 16:09:05,482  INFO: Starting combined data collection...
2025-06-09 16:09:05,482  INFO: 开始合并数据采集流程...
2025-06-09 16:09:15,583  INFO: 正在进行第二次数据收集...
2025-06-09 16:09:46,593  INFO: 数据采集完成：rx_ok差值(39687) > 10000
2025-06-09 16:09:46,594  INFO: 合并数据采集完成
2025-06-09 16:09:46,595  INFO: Calculating PER...
2025-06-09 16:10:13,905  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 16:10:13,958  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 16:10:14,528  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 16:10:14,529  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 16:10:14,530  INFO: 开始配置CMW信号发生器...
2025-06-09 16:10:44,118  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:10:44,119  INFO: 开始加载ARB文件...
2025-06-09 16:10:49,663  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 16:12:08,371  INFO: Waiting for initial data collection...
2025-06-09 16:12:13,372  INFO: Collecting first data...
2025-06-09 16:12:20,979  INFO: Starting combined data collection...
2025-06-09 16:12:20,979  INFO: 开始合并数据采集流程...
2025-06-09 16:12:32,423  INFO: 正在进行第二次数据收集...
2025-06-09 16:12:36,967  INFO: 数据采集完成：rx_ok差值(18974) > 10000
2025-06-09 16:12:36,968  INFO: 合并数据采集完成
2025-06-09 16:12:36,969  INFO: Calculating PER...
2025-06-09 16:12:50,545  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 16:12:50,546  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 16:12:50,681  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 16:12:50,682  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 16:12:50,684  INFO: 开始配置CMW信号发生器...
2025-06-09 16:13:16,107  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:13:16,107  INFO: 开始加载ARB文件...
2025-06-09 16:13:26,509  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 16:15:35,513  INFO: Waiting for initial data collection...
2025-06-09 16:15:50,517  INFO: Collecting first data...
2025-06-09 16:16:21,526  INFO: Starting combined data collection...
2025-06-09 16:16:21,527  INFO: 开始合并数据采集流程...
2025-06-09 16:16:41,054  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:16:41.053', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:16:41,157  INFO: 正在进行第二次数据收集...
2025-06-09 16:16:42,943  INFO: 数据采集完成：rx_ok差值(50629) > 10000
2025-06-09 16:16:42,943  INFO: 合并数据采集完成
2025-06-09 16:16:42,945  INFO: Calculating PER...
2025-06-09 16:16:47,422  INFO: 测试成功 - PER: 0.02%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 16:16:50,074  INFO: 
=== 执行测试用例 6/10 ===
2025-06-09 16:16:50,075  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-09 16:16:50,076  INFO: 开始配置CMW信号发生器...
2025-06-09 16:17:25,517  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:17:25,517  INFO: 开始加载ARB文件...
2025-06-09 16:17:29,420  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-09 16:18:42,514  INFO: Waiting for initial data collection...
2025-06-09 16:18:47,516  INFO: Collecting first data...
2025-06-09 16:18:52,573  INFO: Starting combined data collection...
2025-06-09 16:18:52,577  INFO: 开始合并数据采集流程...
2025-06-09 16:19:00,319  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:19:00.318', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:19:00,420  INFO: 正在进行第二次数据收集...
2025-06-09 16:19:02,568  INFO: 数据采集完成：rx_ok差值(12904) > 10000
2025-06-09 16:19:02,571  INFO: 合并数据采集完成
2025-06-09 16:19:02,573  INFO: Calculating PER...
2025-06-09 16:19:08,540  INFO: 测试成功 - PER: 0.0%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 16:19:08,583  INFO: 
=== 执行测试用例 7/10 ===
2025-06-09 16:19:08,583  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-09 16:19:08,584  INFO: 开始配置CMW信号发生器...
2025-06-09 16:19:09,886  INFO: 测试停止!
2025-06-09 16:20:51,235  INFO: 测试开始!
2025-06-09 16:20:51,236  INFO: 测试进度：0/1
2025-06-09 16:20:51,236  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 16:20:51,237  INFO: 当前执行的用例为：superlink_rx_gauss
2025-06-09 16:20:51,258  INFO: === 初始化测试环境 ===
2025-06-09 16:20:51,258  INFO: 正在初始化设备连接...
2025-06-09 16:20:51,305  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 16:20:51,322  INFO: 正在创建测试报告...
2025-06-09 16:20:51,323  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 16:20:51,324  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_162051
2025-06-09 16:20:51,324  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_162051/superlink_rx_gauss_report_20250609_162051.xlsx
2025-06-09 16:20:51,325  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_gauss_report_20250609_162051/superlink_rx_gauss_report_20250609_162051.xlsx
2025-06-09 16:20:51,325  INFO: === 开始高斯测试 ===
2025-06-09 16:20:51,333  INFO: === 生成高斯测试用例 ===
2025-06-09 16:20:51,334  INFO: 高斯测试参数配置:
2025-06-09 16:20:51,334  INFO:   SYM_RATES: [0]
2025-06-09 16:20:51,334  INFO:   S_CODE_EN: [1]
2025-06-09 16:20:51,335  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 16:20:51,335  INFO:   SNRS: [1, 2, 3, 4, 5, 6]
2025-06-09 16:20:51,335  INFO:   DATA_LEN: [6]
2025-06-09 16:20:51,335  INFO: 测试用例数量(36)超过限制(10)，将只执行前10个用例
2025-06-09 16:20:51,335  INFO: 成功生成 10 个高斯测试用例
2025-06-09 16:20:51,336  INFO: 总测试用例数: 10
2025-06-09 16:20:51,336  INFO: 
=== 执行测试用例 1/10 ===
2025-06-09 16:20:51,336  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 1, 'signal_ch': 0}
2025-06-09 16:20:51,336  INFO: 开始配置CMW信号发生器...
2025-06-09 16:20:52,348  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:20:52,349  INFO: 开始加载ARB文件...
2025-06-09 16:20:52,569  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym0db_grdc.wv"
2025-06-09 16:22:05,028  INFO: Waiting for initial data collection...
2025-06-09 16:22:10,030  INFO: Collecting first data...
2025-06-09 16:22:11,067  INFO: Starting combined data collection...
2025-06-09 16:22:11,067  INFO: 开始合并数据采集流程...
2025-06-09 16:22:14,169  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:22:14.169', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1353e-02'}
2025-06-09 16:22:16,214  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:22:16.214', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.5449e-03'}
2025-06-09 16:22:16,317  INFO: 正在进行第二次数据收集...
2025-06-09 16:22:17,353  INFO: 继续采集：rx_ok差值(3937)在范围内，继续等待...
2025-06-09 16:22:18,472  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:22:18.472', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.5278e-02'}
2025-06-09 16:22:20,519  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:22:20.519', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.5625e-02'}
2025-06-09 16:22:21,345  INFO: 正在进行第二次数据收集...
2025-06-09 16:22:22,378  INFO: 继续采集：rx_ok差值(6869)在范围内，继续等待...
2025-06-09 16:22:23,496  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:22:23.496', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.8311e-02'}
2025-06-09 16:22:25,545  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:22:25.545', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2573e-02'}
2025-06-09 16:22:26,372  INFO: 正在进行第二次数据收集...
2025-06-09 16:22:27,407  INFO: 数据采集完成：rx_ok差值(10030) > 10000
2025-06-09 16:22:27,407  INFO: 合并数据采集完成
2025-06-09 16:22:27,409  INFO: Calculating PER...
2025-06-09 16:22:28,441  INFO: 测试成功 - PER: 32.21%, 极性误差: 8.5449e-03~3.5278e-02
2025-06-09 16:22:28,481  INFO: 
=== 执行测试用例 2/10 ===
2025-06-09 16:22:28,481  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 2, 'signal_ch': 0}
2025-06-09 16:22:28,483  INFO: 开始配置CMW信号发生器...
2025-06-09 16:22:34,555  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:22:34,556  INFO: 开始加载ARB文件...
2025-06-09 16:22:34,786  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym1db_grdc.wv"
2025-06-09 16:23:47,251  INFO: Waiting for initial data collection...
2025-06-09 16:23:52,254  INFO: Collecting first data...
2025-06-09 16:23:53,290  INFO: Starting combined data collection...
2025-06-09 16:23:53,290  INFO: 开始合并数据采集流程...
2025-06-09 16:23:56,374  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:23:56.374', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '2.9297e-03'}
2025-06-09 16:23:58,418  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:23:58.418', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.6621e-03'}
2025-06-09 16:23:58,525  INFO: 正在进行第二次数据收集...
2025-06-09 16:23:59,560  INFO: 继续采集：rx_ok差值(5407)在范围内，继续等待...
2025-06-09 16:24:00,680  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:24:00.680', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.0049e-03'}
2025-06-09 16:24:10,783  INFO: 正在进行第二次数据收集...
2025-06-09 16:24:41,795  INFO: 数据采集完成：rx_ok差值(15887) > 10000
2025-06-09 16:24:41,796  INFO: 合并数据采集完成
2025-06-09 16:24:41,799  INFO: Calculating PER...
2025-06-09 16:25:38,725  INFO: 测试成功 - PER: 13.05%, 极性误差: 2.9297e-03~5.0049e-03
2025-06-09 16:25:38,767  INFO: 
=== 执行测试用例 3/10 ===
2025-06-09 16:25:38,767  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 3, 'signal_ch': 0}
2025-06-09 16:25:38,769  INFO: 开始配置CMW信号发生器...
2025-06-09 16:26:38,537  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:26:38,537  INFO: 开始加载ARB文件...
2025-06-09 16:27:08,571  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym2db_grdc.wv"
2025-06-09 16:29:08,303  INFO: Waiting for initial data collection...
2025-06-09 16:29:18,145  INFO: Collecting first data...
2025-06-09 16:29:23,603  INFO: Starting combined data collection...
2025-06-09 16:29:23,604  INFO: 开始合并数据采集流程...
2025-06-09 16:29:26,953  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:29:26.953', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '7.4463e-03'}
2025-06-09 16:29:32,891  INFO: 正在进行第二次数据收集...
2025-06-09 16:29:36,237  INFO: 数据采集完成：rx_ok差值(14260) > 10000
2025-06-09 16:29:36,238  INFO: 合并数据采集完成
2025-06-09 16:29:36,239  INFO: Calculating PER...
2025-06-09 16:29:37,464  INFO: 测试成功 - PER: 3.22%, 极性误差: 7.4463e-03~7.4463e-03
2025-06-09 16:29:37,510  INFO: 
=== 执行测试用例 4/10 ===
2025-06-09 16:29:37,513  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 4, 'signal_ch': 0}
2025-06-09 16:29:37,515  INFO: 开始配置CMW信号发生器...
2025-06-09 16:29:55,831  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:29:55,832  INFO: 开始加载ARB文件...
2025-06-09 16:29:57,266  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym3db_grdc.wv"
2025-06-09 16:31:06,459  INFO: Waiting for initial data collection...
2025-06-09 16:31:11,461  INFO: Collecting first data...
2025-06-09 16:31:12,500  INFO: Starting combined data collection...
2025-06-09 16:31:12,500  INFO: 开始合并数据采集流程...
2025-06-09 16:31:15,583  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:31:15.582', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.6621e-04'}
2025-06-09 16:31:17,629  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:31:17.629', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-04'}
2025-06-09 16:31:17,735  INFO: 正在进行第二次数据收集...
2025-06-09 16:31:18,769  INFO: 继续采集：rx_ok差值(6249)在范围内，继续等待...
2025-06-09 16:31:19,889  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:31:19.889', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-04'}
2025-06-09 16:31:21,952  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:31:21.952', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.6621e-04'}
2025-06-09 16:31:22,785  INFO: 正在进行第二次数据收集...
2025-06-09 16:31:23,819  INFO: 数据采集完成：rx_ok差值(11272) > 10000
2025-06-09 16:31:23,821  INFO: 合并数据采集完成
2025-06-09 16:31:23,823  INFO: Calculating PER...
2025-06-09 16:31:25,882  INFO: 测试成功 - PER: 0.45%, 极性误差: 1.2207e-04~3.6621e-04
2025-06-09 16:31:25,913  INFO: 
=== 执行测试用例 5/10 ===
2025-06-09 16:31:25,913  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 5, 'signal_ch': 0}
2025-06-09 16:31:25,914  INFO: 开始配置CMW信号发生器...
2025-06-09 16:31:31,986  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:31:31,987  INFO: 开始加载ARB文件...
2025-06-09 16:31:32,217  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym4db_grdc.wv"
2025-06-09 16:32:44,701  INFO: Waiting for initial data collection...
2025-06-09 16:32:49,703  INFO: Collecting first data...
2025-06-09 16:32:50,738  INFO: Starting combined data collection...
2025-06-09 16:32:50,738  INFO: 开始合并数据采集流程...
2025-06-09 16:32:53,809  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:32:53.808', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:32:55,871  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:32:55.871', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:32:55,978  INFO: 正在进行第二次数据收集...
2025-06-09 16:32:57,012  INFO: 继续采集：rx_ok差值(6274)在范围内，继续等待...
2025-06-09 16:32:58,131  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:32:58.131', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:33:00,194  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:33:00.194', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:33:01,027  INFO: 正在进行第二次数据收集...
2025-06-09 16:33:02,058  INFO: 数据采集完成：rx_ok差值(11322) > 10000
2025-06-09 16:33:02,059  INFO: 合并数据采集完成
2025-06-09 16:33:02,060  INFO: Calculating PER...
2025-06-09 16:33:04,118  INFO: 测试成功 - PER: 0.02%, 极性误差: 0.0000e+00~0.0000e+00
2025-06-09 16:33:04,155  INFO: 
=== 执行测试用例 6/10 ===
2025-06-09 16:33:04,155  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 6, 'signal_ch': 0}
2025-06-09 16:33:04,157  INFO: 开始配置CMW信号发生器...
2025-06-09 16:33:10,184  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:33:10,184  INFO: 开始加载ARB文件...
2025-06-09 16:33:10,417  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar2_8\qpsk_1Rs_24Fs_1000fps_PType0_seed0_10bk_sym5db_grdc.wv"
2025-06-09 16:34:22,888  INFO: Waiting for initial data collection...
2025-06-09 16:34:27,895  INFO: Collecting first data...
2025-06-09 16:34:28,926  INFO: Starting combined data collection...
2025-06-09 16:34:28,927  INFO: 开始合并数据采集流程...
2025-06-09 16:34:31,982  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:34:31.982', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.2207e-04'}
2025-06-09 16:34:34,034  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:34:34.033', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:34:34,138  INFO: 正在进行第二次数据收集...
2025-06-09 16:34:35,169  INFO: 继续采集：rx_ok差值(6243)在范围内，继续等待...
2025-06-09 16:34:36,289  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:34:36.289', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:34:38,337  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:34:38.337', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '0.0000e+00'}
2025-06-09 16:34:39,165  INFO: 正在进行第二次数据收集...
2025-06-09 16:34:40,197  INFO: 数据采集完成：rx_ok差值(11270) > 10000
2025-06-09 16:34:40,199  INFO: 合并数据采集完成
2025-06-09 16:34:40,200  INFO: Calculating PER...
2025-06-09 16:34:42,258  INFO: 测试成功 - PER: 0.01%, 极性误差: 0.0000e+00~1.2207e-04
2025-06-09 16:34:42,288  INFO: 
=== 执行测试用例 7/10 ===
2025-06-09 16:34:42,288  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 1, 'signal_ch': 0}
2025-06-09 16:34:42,290  INFO: 开始配置CMW信号发生器...
2025-06-09 16:34:48,321  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:34:48,322  INFO: 开始加载ARB文件...
2025-06-09 16:34:48,334  ERROR: ARB文件不存在: ARB文件: qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym0db_grdc.wv不在D:/sle_stream/test/6byte_Gauss/1M/R1_polar3_8目录下
2025-06-09 16:34:48,366  INFO: 
=== 执行测试用例 8/10 ===
2025-06-09 16:34:48,366  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 2, 'signal_ch': 0}
2025-06-09 16:34:48,368  INFO: 开始配置CMW信号发生器...
2025-06-09 16:34:49,381  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:34:49,381  INFO: 开始加载ARB文件...
2025-06-09 16:34:49,613  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym1db_grdc.wv"
2025-06-09 16:37:01,922  INFO: Waiting for initial data collection...
2025-06-09 16:37:16,924  INFO: Collecting first data...
2025-06-09 16:37:47,934  INFO: Starting combined data collection...
2025-06-09 16:37:47,935  INFO: 开始合并数据采集流程...
2025-06-09 16:37:58,036  INFO: 正在进行第二次数据收集...
2025-06-09 16:38:29,047  INFO: 数据采集完成：rx_ok差值(32307) > 10000
2025-06-09 16:38:29,048  INFO: 合并数据采集完成
2025-06-09 16:38:29,049  INFO: Calculating PER...
2025-06-09 16:39:00,062  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 16:39:00,062  ERROR: 测试失败: min() arg is an empty sequence
2025-06-09 16:39:00,112  INFO: 
=== 执行测试用例 9/10 ===
2025-06-09 16:39:00,112  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 3, 'signal_ch': 0}
2025-06-09 16:39:00,114  INFO: 开始配置CMW信号发生器...
2025-06-09 16:39:36,682  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:39:36,683  INFO: 开始加载ARB文件...
2025-06-09 16:39:36,905  INFO: ARB文件加载成功: "D:\sle_stream\test\6byte_Gauss\1M\R1_polar3_8\qpsk_1Rs_24Fs_1000fps_PType1_seed0_5bk_sym2db_grdc.wv"
2025-06-09 16:40:38,217  INFO: 测试停止!
2025-06-09 16:51:09,279  INFO: 测试开始!
2025-06-09 16:51:09,280  INFO: 测试进度：0/1
2025-06-09 16:51:09,280  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-06-09 16:51:09,281  INFO: 当前执行的用例为：superlink_rx_sensitivity
2025-06-09 16:51:09,308  INFO: === 初始化测试环境 ===
2025-06-09 16:51:09,309  INFO: 正在初始化设备连接...
2025-06-09 16:51:09,376  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-06-09 16:51:09,392  INFO: 正在创建测试报告...
2025-06-09 16:51:09,392  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告
2025-06-09 16:51:09,394  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_sensitivity_report_20250609_165109
2025-06-09 16:51:09,394  INFO: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_sensitivity_report_20250609_165109/superlink_rx_sensitivity_report_20250609_165109.xlsx
2025-06-09 16:51:09,395  INFO: 测试报告创建成功: /Users/<USER>/workspace/bluetooth_test/report/0609/蓝牙指标测试用例集_测试报告/superlink_rx_sensitivity_report_20250609_165109/superlink_rx_sensitivity_report_20250609_165109.xlsx
2025-06-09 16:51:09,395  INFO: === 开始灵敏度测试 ===
2025-06-09 16:51:09,396  INFO: === 生成灵敏度测试用例 ===
2025-06-09 16:51:09,396  INFO: 灵敏度测试参数配置:
2025-06-09 16:51:09,397  INFO:   SYM_RATES: [0]
2025-06-09 16:51:09,397  INFO:   S_CODE_EN: [1]
2025-06-09 16:51:09,401  INFO:   SCODE_RATES: [2, 3, 4, 5, 6, 7]
2025-06-09 16:51:09,402  INFO:   SNRS: [100]
2025-06-09 16:51:09,402  INFO:   DATA_LEN: [6]
2025-06-09 16:51:09,402  INFO: 测试用例数量(6)超过限制(5)，将只执行前5个用例
2025-06-09 16:51:09,403  INFO: 成功生成 5 个灵敏度测试用例
2025-06-09 16:51:09,403  INFO: 总测试用例数: 5
2025-06-09 16:51:09,406  INFO: 
=== 执行测试用例 1/5 ===
2025-06-09 16:51:09,408  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0}
2025-06-09 16:51:09,409  INFO: 开始配置CMW信号发生器...
2025-06-09 16:51:15,422  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:51:15,423  INFO: 开始加载ARB文件...
2025-06-09 16:51:15,643  INFO: ARB文件加载成功: "D:/sle_stream/test/6byte_Sensitivity/1M/qpsk_1Rs_24Fs_1000fps_PType0_seed0_addsine_10bk_sym100db_grdc.wv"
2025-06-09 16:51:20,854  INFO: 初始化RF板...
2025-06-09 16:51:21,872  INFO: 发送测试命令: bb test_cmw 0 0 1 2 6
2025-06-09 16:51:24,932  INFO: ✨ 开始灵敏度测试数据解析
2025-06-09 16:51:24,933  INFO: 🔧 测试参数: default_rms_level=-90dBm, quick_step=5dB, slow_step=1dB, ref_per=10%
2025-06-09 16:51:25,058  INFO: 🔍 步骤1: 默认RMS电平 -90dBm时 并获取初始PER值
2025-06-09 16:51:27,674  INFO: Waiting for initial data collection...
2025-06-09 16:51:32,677  INFO: Collecting first data...
2025-06-09 16:51:33,708  INFO: Starting combined data collection...
2025-06-09 16:51:33,708  INFO: 开始合并数据采集流程...
2025-06-09 16:51:36,798  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:51:36.797', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.3711e-03'}
2025-06-09 16:51:38,860  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:51:38.860', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '8.4229e-03'}
2025-06-09 16:51:38,966  INFO: 正在进行第二次数据收集...
2025-06-09 16:51:39,998  INFO: 继续采集：rx_ok差值(6128)在范围内，继续等待...
2025-06-09 16:51:41,117  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:51:41.116', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.5400e-03'}
2025-06-09 16:51:43,167  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:51:43.166', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.6152e-03'}
2025-06-09 16:51:43,981  INFO: 正在进行第二次数据收集...
2025-06-09 16:51:45,029  INFO: 数据采集完成：rx_ok差值(10961) > 10000
2025-06-09 16:51:45,034  INFO: 合并数据采集完成
2025-06-09 16:51:45,036  INFO: Calculating PER...
2025-06-09 16:51:47,088  INFO: 📊 初始测试结果: RMS电平=-90dBm, PER=2.88%
2025-06-09 16:51:47,089  INFO: 🚀 步骤2.1: 开始快速步进阶段
2025-06-09 16:51:47,091  INFO: 🔽 快速步进 1: 设置RMS电平为 -95dBm
2025-06-09 16:51:49,823  INFO: Waiting for initial data collection...
2025-06-09 16:51:54,829  INFO: Collecting first data...
2025-06-09 16:51:55,863  INFO: Starting combined data collection...
2025-06-09 16:51:55,864  INFO: 开始合并数据采集流程...
2025-06-09 16:51:58,934  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:51:58.934', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '4.2725e-03'}
2025-06-09 16:52:00,996  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:00.996', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '5.6152e-03'}
2025-06-09 16:52:01,102  INFO: 正在进行第二次数据收集...
2025-06-09 16:52:02,138  INFO: 继续采集：rx_ok差值(6085)在范围内，继续等待...
2025-06-09 16:52:03,256  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:03.256', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.1738e-03'}
2025-06-09 16:52:05,303  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:05.302', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.0518e-03'}
2025-06-09 16:52:06,135  INFO: 正在进行第二次数据收集...
2025-06-09 16:52:07,170  INFO: 数据采集完成：rx_ok差值(10950) > 10000
2025-06-09 16:52:07,172  INFO: 合并数据采集完成
2025-06-09 16:52:07,173  INFO: Calculating PER...
2025-06-09 16:52:09,230  INFO: 📊 快速步进结果: RMS电平=-95dBm, PER=3.0%
2025-06-09 16:52:09,230  INFO: 🔽 快速步进 2: 设置RMS电平为 -100dBm
2025-06-09 16:52:11,984  INFO: Waiting for initial data collection...
2025-06-09 16:52:16,985  INFO: Collecting first data...
2025-06-09 16:52:18,019  INFO: Starting combined data collection...
2025-06-09 16:52:18,020  INFO: 开始合并数据采集流程...
2025-06-09 16:52:21,120  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:21.120', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.8555e-02'}
2025-06-09 16:52:23,181  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:23.181', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.9531e-02'}
2025-06-09 16:52:23,283  INFO: 正在进行第二次数据收集...
2025-06-09 16:52:24,317  INFO: 继续采集：rx_ok差值(5819)在范围内，继续等待...
2025-06-09 16:52:25,436  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:25.436', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.1230e-02'}
2025-06-09 16:52:27,498  DEBUG: Register data collected: {'timestamp': '2025-06-09 16:52:27.498', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '1.0986e-02'}
2025-06-09 16:52:28,330  INFO: 正在进行第二次数据收集...
2025-06-09 16:52:29,362  INFO: 数据采集完成：rx_ok差值(10503) > 10000
2025-06-09 16:52:29,363  INFO: 合并数据采集完成
2025-06-09 16:52:29,365  INFO: Calculating PER...
2025-06-09 16:52:31,420  INFO: 📊 快速步进结果: RMS电平=-100dBm, PER=7.06%
2025-06-09 16:52:31,421  INFO: 🔽 快速步进 3: 设置RMS电平为 -105dBm
2025-06-09 16:52:44,169  INFO: Waiting for initial data collection...
2025-06-09 16:52:59,171  INFO: Collecting first data...
2025-06-09 16:53:27,080  INFO: Starting combined data collection...
2025-06-09 16:53:27,081  INFO: 开始合并数据采集流程...
2025-06-09 16:53:37,182  INFO: 正在进行第二次数据收集...
2025-06-09 16:54:08,200  INFO: 数据采集完成：rx_ok差值(22079) > 10000
2025-06-09 16:54:08,201  INFO: 合并数据采集完成
2025-06-09 16:54:08,202  INFO: Calculating PER...
2025-06-09 16:54:39,214  ERROR: Error in data collection: min() arg is an empty sequence
2025-06-09 16:55:10,222  INFO: 灵敏度测试状态清理完成
2025-06-09 16:55:10,233  ERROR: 测试失败: 快速步进阶段测试失败: min() arg is an empty sequence
2025-06-09 16:55:10,276  INFO: 
=== 执行测试用例 2/5 ===
2025-06-09 16:55:10,276  INFO: 
开始执行测试用例: {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 3, 'snr': 100, 'signal_ch': 0}
2025-06-09 16:55:10,279  INFO: 开始配置CMW信号发生器...
2025-06-09 16:56:08,838  INFO: CMW信号发生器配置完成，频率: 2402.007MHz
2025-06-09 16:56:08,839  INFO: 开始加载ARB文件...
2025-06-09 16:56:38,871  INFO: ARB文件加载成功: "D:/sle_stream/test/6byte_Sensitivity/1M/qpsk_1Rs_24Fs_1000fps_PType1_seed0_addsine_5bk_sym100db_grdc.wv"
2025-06-09 16:57:13,897  INFO: 发送测试命令: bb test_cmw 0 0 1 3 6
2025-06-09 16:58:36,917  INFO: ✨ 开始灵敏度测试数据解析
2025-06-09 16:58:36,918  INFO: 🔧 测试参数: default_rms_level=-90dBm, quick_step=5dB, slow_step=1dB, ref_per=10%
2025-06-09 16:58:36,920  INFO: 🔍 步骤1: 默认RMS电平 -90dBm时 并获取初始PER值
2025-06-09 16:58:50,852  INFO: Waiting for initial data collection...
2025-06-09 16:58:52,394  INFO: 测试停止!
2025-06-09 16:58:55,857  INFO: 灵敏度测试状态清理完成
