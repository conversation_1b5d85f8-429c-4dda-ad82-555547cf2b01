#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Excel报告写入功能
"""

import os
import sys
import time

# 添加模块路径
sys.path.append('module_set')

from module_set.handle_report_class import HandleExcelReport

def test_basic_write():
    """测试基本的写入功能"""
    print("=== 测试基本写入功能 ===")
    
    # 创建测试报告文件
    test_file = "test_reports/fix_test_report.xlsx"
    
    # 确保目录存在
    os.makedirs(os.path.dirname(test_file), exist_ok=True)
    
    # 创建报告处理对象
    handle = HandleExcelReport(filename=test_file)
    
    # 测试参数
    test_params = {
        'sym_rate': 0,
        'signal_ch': 10,
        's_code_en': 1,
        'scode_rate': 4,
        'snr': 2,
        'data_len': 6,
        'test_fre': 2412.007
    }
    
    # 测试结果（成功情况）
    test_result = {
        'per': 0.05,
        'polar_err': '1.2e-03~3.4e-03',
        'first_data': {'rx_ok': 1000, 'crc_err': 5},
        'second_data': {'rx_ok': 2000, 'crc_err': 10}
    }
    
    print("1. 测试成功情况的数据写入...")
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='6byte_Gauss',
        test_func='Gauss',
        test_params=test_params,
        test_result=test_result
    )
    print(f"写入结果: {'成功' if success else '失败'}")
    
    print("2. 测试失败情况的数据写入...")
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='6byte_Gauss',
        test_func='Gauss',
        test_params=test_params,
        test_result=None,
        error_msg="测试设备连接超时"
    )
    print(f"写入结果: {'成功' if success else '失败'}")
    
    print("3. 测试Sensitivity数据写入...")
    test_params_sens = {
        'sym_rate': 1,
        'signal_ch': 15,
        's_code_en': 0,
        'scode_rate': 3,
        'snr': 1,
        'data_len': 10,
        'test_fre': 2437.0
    }
    
    test_result_sens = {
        'sensitivity': -85.5,
        'per': 10.2,
        'polar_err': '2.1e-04~5.6e-04',
        'first_data': {'rx_ok': 800, 'crc_err': 80},
        'second_data': {'rx_ok': 1600, 'crc_err': 160}
    }
    
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='10byte_Sensitivity',
        test_func='Sensitivity',
        test_params=test_params_sens,
        test_result=test_result_sens
    )
    print(f"写入结果: {'成功' if success else '失败'}")
    
    # 检查文件是否创建成功
    if os.path.exists(test_file):
        file_size = os.path.getsize(test_file)
        print(f"✅ 报告文件创建成功: {test_file} (大小: {file_size} bytes)")
        return True
    else:
        print(f"❌ 报告文件未创建: {test_file}")
        return False

def test_multiple_writes():
    """测试多次写入是否会卡死"""
    print("\n=== 测试多次写入功能 ===")
    
    test_file = "test_reports/multiple_write_test.xlsx"
    handle = HandleExcelReport(filename=test_file)
    
    success_count = 0
    total_tests = 5
    
    for i in range(total_tests):
        print(f"执行第 {i+1} 次写入...")
        
        test_params = {
            'sym_rate': i % 3,
            'signal_ch': 10 + i,
            's_code_en': i % 2,
            'scode_rate': 4,
            'snr': 2,
            'data_len': 6,
            'test_fre': 2412.007 + i
        }
        
        test_result = {
            'per': 0.05 + i * 0.01,
            'polar_err': f'{1.2e-03 + i * 1e-04:.4e}~{3.4e-03 + i * 1e-04:.4e}',
            'first_data': {'rx_ok': 1000 + i * 100, 'crc_err': 5 + i},
            'second_data': {'rx_ok': 2000 + i * 100, 'crc_err': 10 + i}
        }
        
        success = handle.write_sagitta_test_data_with_error_handling(
            sheet_name='MultiTest_Gauss',
            test_func='Gauss',
            test_params=test_params,
            test_result=test_result
        )
        
        if success:
            success_count += 1
            print(f"  第 {i+1} 次写入成功")
        else:
            print(f"  第 {i+1} 次写入失败")
        
        # 短暂延迟
        time.sleep(0.1)
    
    print(f"多次写入测试完成: {success_count}/{total_tests} 成功")
    return success_count == total_tests

def main():
    """主测试函数"""
    print("开始测试修复后的Excel报告写入功能...")
    
    try:
        # 测试基本写入功能
        basic_test_passed = test_basic_write()
        
        # 测试多次写入功能
        multiple_test_passed = test_multiple_writes()
        
        # 总结测试结果
        print("\n=== 测试结果总结 ===")
        print(f"基本写入测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
        print(f"多次写入测试: {'✅ 通过' if multiple_test_passed else '❌ 失败'}")
        
        if basic_test_passed and multiple_test_passed:
            print("🎉 所有测试通过！Excel报告写入功能已修复。")
            return True
        else:
            print("⚠️  部分测试失败，请检查错误信息。")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
