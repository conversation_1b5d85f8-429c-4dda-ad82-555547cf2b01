# -*- coding: utf-8 -*-
import os
import platform
import re
import time
import shutil
import tempfile
import threading
import json
from datetime import datetime

from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill
from openpyxl.styles import Border, Side, colors
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
from openpyxl.comments import Comment


class HandleExcelReport:

    def __init__(self, filename):
        self.filename = filename
        self._backup_dir = os.path.join(os.path.dirname(filename), 'backup')  # 备份目录

        self._ensure_backup_dir()

    def _ensure_backup_dir(self):
        """确保备份目录存在"""
        if not os.path.exists(self._backup_dir):
            try:
                os.makedirs(self._backup_dir, exist_ok=True)
            except Exception as e:
                print(f"Warning: Failed to create backup directory: {e}")

    def _is_excel_file_valid(self, file_path):
        """
        检查Excel文件是否正常可用

        Args:
            file_path: 文件路径

        Returns:
            bool: 文件是否正常
        """
        if not os.path.exists(file_path):
            return False

        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size < 1024:  # 小于1KB可能是损坏文件
                print(f"Warning: File {file_path} is too small ({file_size} bytes)")
                return False

            # 尝试打开Excel文件
            wb = load_workbook(file_path)

            # 检查是否有工作表
            if len(wb.sheetnames) == 0:
                print(f"Warning: File {file_path} has no worksheets")
                wb.close()
                return False

            wb.close()
            return True

        except Exception as e:
            print(f"Warning: File {file_path} validation failed: {e}")
            return False

    def _get_master_backup_path(self):
        """
        获取主备份文件路径（固定名称）

        Returns:
            str: 主备份文件路径
        """
        base_name = os.path.splitext(os.path.basename(self.filename))[0]
        backup_filename = f"{base_name}_master_backup.xlsx"
        return os.path.join(self._backup_dir, backup_filename)

    def _create_master_backup(self):
        """
        创建主备份文件（固定名称，不带时间戳）

        Returns:
            str: 备份文件路径，失败时返回None
        """
        if not os.path.exists(self.filename):
            return None

        try:
            backup_path = self._get_master_backup_path()
            shutil.copy2(self.filename, backup_path)
            print(f"Master backup created: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"Warning: Failed to create master backup: {e}")
            return None

    def _restore_from_master_backup(self):
        """
        从主备份文件恢复

        Returns:
            bool: 恢复是否成功
        """
        backup_path = self._get_master_backup_path()

        if not os.path.exists(backup_path):
            print(f"Warning: Master backup file not found: {backup_path}")
            return False

        # 检查备份文件是否正常
        if not self._is_excel_file_valid(backup_path):
            print(f"Warning: Master backup file is corrupted: {backup_path}")
            return False

        try:
            shutil.copy2(backup_path, self.filename)
            print(f"File restored from master backup: {backup_path}")
            return True
        except Exception as e:
            print(f"Error: Failed to restore from master backup: {e}")
            return False

    def report_file_is_exist(self):
        # 确保目录存在
        dir_path = os.path.dirname(self.filename)
        if dir_path and not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                print(f"Warning: Failed to create directory {dir_path}: {e}")
                # 如果目录创建失败，尝试使用当前目录
                self.filename = os.path.basename(self.filename)
        if not os.path.exists(self.filename):
            wb = Workbook()
            wb.remove(wb.active)
        else:
            wb = load_workbook(self.filename)
        return wb

    def get_or_create_sheet(self, sheet_name):
        wb = self.report_file_is_exist()
        sheets_name = wb.sheetnames
        if sheet_name in sheets_name:
            ws = wb[sheet_name]
        else:
            ws = wb.create_sheet(sheet_name)
        return wb, ws

    def write_report_image_data(
            self, sheet_name, image_data, col_width=None,
            row_height=380, interval_row=2):
        """
        将图片二进制数据写入excel
            * sheet_name:   excel页名称，用于指定图片数据保存的页
            * chart_data:   图片数据，此处为二进制图片数据
            * col_width:    列宽度，用于设置图片所在列的宽度，应略大于图片的宽度
            * row_height:   行高度，用于设置图片所在行的高度，应略大于图片的高度
            * interval_row: 每次追加写图片时，与之前数据的间隔行数
        """
        if col_width is None:
            width_val = 45
        else:
            width_val = col_width
        wb, ws = self.get_or_create_sheet(sheet_name=sheet_name)
        max_row = ws.max_row
        max_col = ws.max_column
        print(max_row, max_col)
        img = Image(image_data)
        if max_row == 1 and max_col == 1:   # 初始sheet表无内容时，获取行列值都为1，与加载的sheet表只有行列都为1且有数据容易混淆
            insert_row = 1
            ws["A1"] = " "
            ws["B1"] = " "
        # elif max_row == 1 and max_col != 1:
        #     insert_row = max_row + 1
        else:
            insert_row = max_row + interval_row
        insert_pos = "A{}".format(insert_row)
        ws[insert_pos] = " "    # 对插入图片的单元格写入空数据，保证max_row的行数正确，因为图片不是单元格数据，max_row不识别图片
        ws.add_image(img, insert_pos)
        # if col_width is None:
        #     if self.get_system_name() == "Linux":
        #         width_val = self.get_cell_width_from_linux_version(org_width_val=width_val)
        #     else:
        #         width_val = self.get_cell_width_from_windows_version(org_width_val=width_val)
        #     # print("width_val: {}".format(width_val))
        #     ws.column_dimensions['A'].width = width_val         # 修改列A的宽
        # else:
        #     ws.column_dimensions['A'].width = width_val
        ws.row_dimensions[insert_row].height = row_height   # 修改列第insert_row行的高
        self.save_workbook(wb)

    def save_workbook(self, wb):
        wb.save(self.filename)

    @staticmethod
    def get_system_name():
        return platform.system()

    @staticmethod
    def get_linux_system_version():
        ret = platform.version()        # '#110~20.04.1-Ubuntu SMP Tue Feb 13 14:25:03 UTC 2024'
        ret_split = re.split(r'[-~]', ret)
        version = ret_split[1][:5]
        return version

    @staticmethod
    def get_windows_system_version():
        ret = platform.platform()       # 'Windows-7-6.1.7601-SP1' or 'Windows-XP-5.1.2600-SP3'
        ret_split = re.split(r'-', ret)
        version = "_".join(ret_split[:2])   # 'Windows_7' or 'Windows_XP'
        return version

    def get_cell_width_for_diff_system(self, org_width_val=8.5):
        if self.get_system_name() == "Linux":
            width = self.get_cell_width_from_linux_version(org_width_val=org_width_val)
        elif self.get_system_name() == "Windows":
            width = self.get_cell_width_from_windows_version(org_width_val=org_width_val)
        else:
            width = org_width_val
        return width

    def get_cell_width_from_linux_version(self, org_width_val=8.5):
        factor = 2
        version = self.get_linux_system_version()
        if version == "16.04":
            width = org_width_val * factor
        elif version == "18.04":
            width = org_width_val * factor
        elif version == "20.04":
            width = org_width_val
        else:
            width = org_width_val
        return width

    def get_cell_width_from_darwin_version(self, org_width_val=8.5):
        return org_width_val

    def get_cell_width_from_windows_version(self, org_width_val=8.5):
        factor = 2
        version = self.get_windows_system_version()
        if version == "Windows_7":
            width = org_width_val * factor
        elif version == "Windows_XP":
            width = org_width_val * factor
        elif version == "Windows_10":
            width = org_width_val * factor
        elif version == "Windows_11":
            width = org_width_val * factor
        else:
            width = org_width_val
        return width

    def write_data_to_excel(self, sheet_name, data, is_title=False):
        alignment = Alignment(horizontal="center", vertical="center", wrapText=True)
        green_fill = PatternFill(patternType="solid", fgColor="00FF00")
        all_border = Border(
            left=Side(style='thin', color=colors.BLACK), right=Side(style='thin', color=colors.BLACK),
            top=Side(style='thin', color=colors.BLACK), bottom=Side(style='thin', color=colors.BLACK))
        wb, ws = self.get_or_create_sheet(sheet_name=sheet_name)
        max_row = ws.max_row
        max_col = ws.max_column
        if max_row == 1 and max_col == 1:   # 初始sheet表无内容时
            start_row = max_row
        else:
            start_row = max_row + 1
        for i in range(len(data)):
            if type(data[i]) in [dict, list]:
                ws.cell(start_row, i + 1).value = str(data[i])
            else:
                ws.cell(start_row, i + 1).value = data[i]
            ws.cell(start_row, i + 1).alignment = alignment
            ws.cell(start_row, i + 1).border = all_border
            col_letters = get_column_letter(i + 1)
            if self.get_system_name() == "Linux":
                width_val = self.get_cell_width_from_linux_version()
            elif self.get_system_name() == "Windows":                       # "Windows"
                width_val = self.get_cell_width_from_windows_version()
            else:
                width_val = 17
            # print("width_val: {}".format(width_val))
            ws.column_dimensions[col_letters].width = width_val

            if is_title:
                ws.cell(start_row, i + 1).fill = green_fill
        self.save_workbook(wb)

    def write_report_title_data(self, sheet_name, data):
        is_title = True
        self.write_data_to_excel(sheet_name=sheet_name, data=data, is_title=is_title)

    def write_report_test_data(self, sheet_name, data):
        self.write_data_to_excel(sheet_name=sheet_name, data=data)

    def is_sheet_empty(self, sheet_name):
        """
        检查指定sheet是否为空（没有实际数据内容）

        Args:
            sheet_name: sheet名称

        Returns:
            bool: True表示sheet为空，False表示sheet有数据
        """
        wb, ws = self.get_or_create_sheet(sheet_name=sheet_name)

        # 检查是否有任何非空单元格
        for row in ws.iter_rows():
            for cell in row:
                if cell.value is not None and str(cell.value).strip() != "":
                    return False
        return True

    def get_sagitta_test_headers(self, test_func):
        """
        根据测试功能获取sagitta项目的表头定义

        Args:
            test_func: 测试功能类型，支持 'Gauss', 'Sensitivity' 等

        Returns:
            list: 表头列表
        """
        headers_map = {
            'Gauss': [
                'Symbol Rate', 'Signal Channel', 'S-Code Enable',
                'S-Code Rate', 'SNR', 'Data Length', 'Test Freq(MHz)',
                'Rd_data1', 'Rd_data2', 'PER(%)', 'Polar Error'
            ],
            'Sensitivity': [
                'Symbol Rate', 'Signal Channel', 'S-Code Enable',
                'S-Code Rate', 'SNR', 'Data Length', 'Test Freq(MHz)',
                'Rd_data1', 'Rd_data2', '灵敏度(dBm)', 'PER(%)', 'Polar Error'
            ],
            'CI': [
                'Symbol Rate', 'Signal Channel', 'S-Code Enable',
                'S-Code Rate', 'SNR', 'Data Length', '有用信号频率(MHz)',
                'CMW功率(dBm)', '干扰信号频率偏移', '干扰信号频率(MHz)', '干扰信号功率(dBm)',
                'Rd_data1', 'Rd_data2', 'PER (%)', 'Polar Error'
            ],
            'Default': [
                'Test Parameter', 'Test Value', 'Result', 'Status'
            ]
        }

        return headers_map.get(test_func, headers_map['Default'])

    def write_sagitta_test_data_with_auto_header(self, sheet_name, test_func, data):
        """
        为sagitta项目写入测试数据，当检测到sheet为空时自动写入表头

        Args:
            sheet_name: sheet名称
            test_func: 测试功能类型（'Gauss', 'Sensitivity', 'CI'等）
            data: 要写入的数据行（单行数据）

        Example:
            # Gauss测试数据写入
            gauss_data = [0, 10, 1, 4, 2, 6, 2412.007, 0.05, '1.2e-03~3.4e-03']
            handle.write_sagitta_test_data_with_auto_header('6byte_Gauss', 'Gauss', gauss_data)

            # Sensitivity测试数据写入
            sens_data = [0, 10, 1, 4, 6, 2412.007, -85, 0.1, -84.5]
            handle.write_sagitta_test_data_with_auto_header('6byte_Sensitivity', 'Sensitivity', sens_data)
        """
        # 检查sheet是否为空，如果为空则先写入表头
        if self.is_sheet_empty(sheet_name):
            headers = self.get_sagitta_test_headers(test_func)
            self.write_report_title_data(sheet_name=sheet_name, data=headers)

        # 写入测试数据
        self.write_report_test_data(sheet_name=sheet_name, data=data)

    def write_sagitta_test_data_with_error_handling(self, sheet_name, test_func, test_params, test_result=None, error_msg=None, max_retries=1):
        """
        简化版的sagitta测试数据写入方法，修复死锁问题

        Args:
            sheet_name: sheet名称
            test_func: 测试功能类型（'Gauss', 'Sensitivity'等）
            test_params: 测试参数字典
            test_result: 测试结果字典（成功时）或None（失败时）
            error_msg: 错误信息（失败时）
            max_retries: 最大重试次数

        Returns:
            bool: 写入是否成功
        """

        # 参数验证
        if not sheet_name or not test_func or not test_params:
            print("Error: Missing required parameters")
            return False

        try:
            # 简化的写入逻辑，避免复杂的线程锁和缓存机制
            return self._simple_write_data(sheet_name, test_func, test_params, test_result, error_msg)
        except Exception as e:
            print(f"Error in write_sagitta_test_data_with_error_handling: {e}")
            return False

    def _simple_write_data(self, sheet_name, test_func, test_params, test_result, error_msg):
        """
        智能备份的数据写入方法

        流程：
        1. 在写入前检查文件是否正常
        2. 如果正常则备份，如果不正常则从备份恢复
        3. 执行数据写入
        """
        try:
            # 步骤1：智能备份和验证
            print(f"Validating file before writing to {sheet_name}...")
            if not self._smart_backup_and_validate():
                print("Warning: File validation failed, but will attempt to write anyway")

            # 步骤2：检查sheet是否为空，如果为空则先写入表头
            if self.is_sheet_empty(sheet_name):
                headers = self.get_sagitta_test_headers(test_func)
                self.write_report_title_data(sheet_name=sheet_name, data=headers)

            # 步骤3：根据测试类型准备数据行
            if test_func == 'Gauss':
                data_row = self._prepare_gauss_data_row(test_params, test_result, error_msg)
            elif test_func == 'Sensitivity':
                data_row = self._prepare_sensitivity_data_row(test_params, test_result, error_msg)
            elif test_func == 'CI':
                data_row = self._prepare_ci_data_row(test_params, test_result, error_msg)
            else:
                data_row = self._prepare_default_data_row(test_params, test_result, error_msg)

            # 步骤4：写入数据行
            self._write_data_row_with_comment(sheet_name, data_row, error_msg, test_func)

            # 步骤5：写入成功后再次验证并更新备份
            if self._is_excel_file_valid(self.filename):
                self._create_master_backup()
                print(f"Data written successfully to {sheet_name} and backup updated")
            else:
                print(f"Warning: Data written to {sheet_name} but file validation failed")

            return True

        except Exception as e:
            print(f"Error in _simple_write_data: {e}")
            # 如果写入失败，尝试从备份恢复
            print("Attempting to restore from backup due to write failure...")
            if self._restore_from_master_backup():
                print("File restored from backup, you may retry the operation")
            return False

    def get_backup_info(self):
        """
        获取备份文件信息

        Returns:
            dict: 备份文件信息
        """
        backup_path = self._get_master_backup_path()

        info = {
            'backup_path': backup_path,
            'backup_exists': os.path.exists(backup_path),
            'backup_valid': False,
            'backup_size': 0,
            'backup_modified': None,
            'main_file_valid': self._is_excel_file_valid(self.filename)
        }

        if info['backup_exists']:
            try:
                info['backup_valid'] = self._is_excel_file_valid(backup_path)
                info['backup_size'] = os.path.getsize(backup_path)
                info['backup_modified'] = datetime.fromtimestamp(os.path.getmtime(backup_path)).isoformat()
            except Exception as e:
                print(f"Warning: Failed to get backup info: {e}")

        return info

    def force_create_backup(self):
        """
        强制创建备份（无论文件是否正常）

        Returns:
            bool: 是否成功
        """
        if os.path.exists(self.filename):
            return self._create_master_backup() is not None
        else:
            print(f"Warning: Main file {self.filename} does not exist")
            return False

    def force_restore_from_backup(self):
        """
        强制从备份恢复（无论主文件是否正常）

        Returns:
            bool: 是否成功
        """
        return self._restore_from_master_backup()

    def _prepare_gauss_data_row(self, test_params, test_result, error_msg):
        """
        准备Gauss测试的数据行
        """
        # 基础测试参数（总是写入）
        data_row = [
            test_params.get('sym_rate', ''),
            test_params.get('signal_ch', ''),
            test_params.get('s_code_en', ''),
            test_params.get('scode_rate', ''),
            test_params.get('snr', ''),
            test_params.get('data_len', ''),
            test_params.get('test_fre', '')
        ]

        # 根据测试结果填充结果数据
        if test_result is not None and error_msg is None:
            # 测试成功情况
            data_row.extend([
                test_result.get('first_data', ''),  # Rd_data1
                test_result.get('second_data', ''),  # Rd_data2
                test_result.get('per', ''),       # PER (%)
                test_result.get('polar_err', '')  # Polar Error
            ])
        else:
            # 测试失败情况
            data_row.extend([
                '',      # Rd_data1 - 空
                '',      # Rd_data2 - 空
                'Fail',  # PER (%) - Fail
                'Fail'   # Polar Error - Fail
            ])

        return data_row

    def _prepare_sensitivity_data_row(self, test_params, test_result, error_msg):
        """
        准备Sensitivity测试的数据行
        """
        # 基础测试参数（总是写入）
        data_row = [
            test_params.get('sym_rate', ''),
            test_params.get('signal_ch', ''),
            test_params.get('s_code_en', ''),
            test_params.get('scode_rate', ''),
            test_params.get('snr', ''),
            test_params.get('data_len', ''),
            test_params.get('test_fre', ''),
        ]

        # 根据测试结果填充结果数据
        if test_result is not None and error_msg is None:
            # 测试成功情况
            data_row.extend([
                test_result.get('first_data', ''),      # Rd_data1
                test_result.get('second_data', ''),     # Rd_data2
                test_result.get('sensitivity', ''),     # Sensitivity (dBm)
                test_result.get('per', ''),             # PER (%)
                test_result.get('polar_err', '')        # Polar Error
            ])
        else:
            # 测试失败情况
            data_row.extend([
                '',  # Rd_data1 - 空
                '',  # Rd_data2 - 空
                'Fail',  # Sensitivity (dBm) - Fail
                'Fail',  # PER (%) - Fail
                'Fail'   # Polar Error - Fail
            ])

        return data_row

    def _prepare_ci_data_row(self, test_params, test_result, error_msg):
        """
        准备CI测试的数据行
        """
        # 基础测试参数（总是写入）
        data_row = [
            test_params.get('sym_rate', ''),
            test_params.get('signal_ch', ''),
            test_params.get('s_code_en', ''),
            test_params.get('scode_rate', ''),
            test_params.get('snr', ''),
            test_params.get('data_len', ''),
            test_params.get('useful_fre', ''),
            test_params.get('useful_level', ''),
            test_params.get('inf_fre_offset_str', ''),
            test_params.get('inf_fre', '')
        ]

        # 根据测试结果填充结果数据
        if test_result is not None and error_msg is None:
            # 测试成功情况
            data_row.extend([
                test_result.get('ci', ''),              # C/I (dBm)
                test_result.get('first_data', ''),      # Rd_data1
                test_result.get('second_data', ''),     # Rd_data2
                test_result.get('per', ''),             # PER (%)
                test_result.get('polar_err', '')        # Polar Error
            ])
        else:
            # 测试失败情况
            data_row.extend([
                'Fail',     # C/I (dBm) - Fail
                '',         # Rd_data1 - 空
                '',         # Rd_data2 - 空
                'Fail',     # PER (%) - Fail
                'Fail'      # Polar Error - Fail
            ])

        return data_row

    def _prepare_default_data_row(self, test_params, test_result, error_msg):
        """
        准备默认测试的数据行
        """
        if test_result is not None and error_msg is None:
            return [
                test_params.get('parameter', ''),
                test_params.get('value', ''),
                test_result.get('result', ''),
                'Pass'
            ]
        else:
            return [
                test_params.get('parameter', ''),
                test_params.get('value', ''),
                'Fail',
                'Fail'
            ]

    def _write_data_row_with_comment(self, sheet_name, data_row, error_msg, test_func='Gauss'):
        """
        写入数据行，并在PER列添加错误批注（如果有错误）
        """
        # 获取工作表
        wb, ws = self.get_or_create_sheet(sheet_name=sheet_name)

        # 计算插入行位置
        max_row = ws.max_row
        max_col = ws.max_column
        if max_row == 1 and max_col == 1:
            start_row = max_row
        else:
            start_row = max_row + 1

        # 写入数据并设置格式
        alignment = Alignment(horizontal="center", vertical="center", wrapText=True)
        all_border = Border(
            left=Side(style='thin', color=colors.BLACK),
            right=Side(style='thin', color=colors.BLACK),
            top=Side(style='thin', color=colors.BLACK),
            bottom=Side(style='thin', color=colors.BLACK)
        )
        red_fill = PatternFill(patternType="solid", fgColor="FF0000")

        per_col_index = None  # PER列的索引

        for i in range(len(data_row)):
            cell = ws.cell(start_row, i + 1)
            if type(data_row[i]) in [dict, list]:
                cell.value = str(data_row[i])
            else:
                cell.value = data_row[i]
            cell.alignment = alignment
            cell.border = all_border

            # 设置列宽
            col_letters = get_column_letter(i + 1)

            if test_func == 'Gauss':
                if i in [0, 1, 2, 3, 4, 5, 6]:
                    org_width_val = 9
                elif i in [7, 8]:
                    org_width_val = 35
                else:
                    org_width_val = 20
            elif test_func == 'Sensitivity':
                if i in [0, 1, 2, 3, 4, 5, 6]:
                    org_width_val = 9
                elif i in [7, 8]:
                    org_width_val = 35
                else:
                    org_width_val = 20
            else:
                org_width_val = 18
            width_val = self.get_cell_width_for_diff_system(org_width_val=org_width_val)
            ws.column_dimensions[col_letters].width = width_val

            # 记录PER列的位置（用于添加批注）
            headers = self.get_sagitta_test_headers(test_func)
            if i < len(headers) and 'PER' in headers[i]:
                per_col_index = i + 1

        # 如果有错误信息且找到了PER列，添加批注
        if error_msg and per_col_index:
            per_cell = ws.cell(start_row, per_col_index)
            per_cell.fill = red_fill
            comment = Comment(error_msg, "System")
            per_cell.comment = comment

        # 保存工作簿
        self.save_workbook(wb)
