import time

from module_set.device_base_api_class import DeviceBaseApi


class VsgApi(DeviceBaseApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def vsg_device_set_preset(self):
        """
        * Full cmd format: ":SYSTem:PRESet"
        * This command returns the signal generator to a set of defined conditions. It is
            equivalent to pressing the front-panel Preset key.
        """
        cmd = ":SYSTem:PRESet"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_set_preset_all(self):
        """
        * Full cmd format: ":SYSTem:PRESet:ALL"
        * This command sets all states of the signal generator back to their factory
            default settings, including states that are not normally affected by signal
            generator power–on, preset, or *RST.
        """
        cmd = ":SYSTem:PRESet:ALL"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_set_preset_user(self):
        """
        * Full cmd format: ":SYSTem:PRESet:USER"
        * This command presets the signal generator to the user’s saved state.
        """
        cmd = ":SYSTem:PRESet:USER"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_set_preset_remote_language(self, language: str = "SCPI"):
        """
        * Full cmd format: ":SYSTem:PRESet:LANGuage <language>"
        * <language> :
            * "SCPI"|"COMP"|"8648"|"E4428C"|"E4438C"|"E8257D"|"E8267D"|"E8
                663B"|"E8247C"|"E8257C"|"E8267C"|"N5181A"|"N5182A"|"E442XB"|
                "E443XB"|"E8241A"|"E8244A"|"E8251A"|"E8254A"|"SMU200A"|"SMAT
                E200A"|"SMJ100A"|"SMIQ"|"SML"|"SMV"|"SMR"|"SMF100A"|"MG3691B
                "|"MG3692B"|"MG3693B"|"MG3694B"|"3410"|"8360"|"8372"|"83732"
                |"83752"|"8340"|"8644"|"8662"|"8663"|"8664"|"8665"
        * This command sets the remote language that is available when the signal
            generator is preset.
        """
        cmd = ":SYSTem:PRESet:LANGuage {}".format(language)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_set_output_frequency(self, value: int or float, unit: str, mode: str = "CW"):
        """
        * Full cmd format: "[:SOURce]:FREQuency:<mode> <value><unit>"
        * <mode>:
            * CW
            * FIXed
        * <unit>:
            * Hz | KHz | MHz | GHz
        * This command sets the signal generator output frequency.
        """
        cmd = ":FREQuency:{} {}{}".format(mode, value, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_output_frequency(self, mode: str = "CW"):
        """
        * Full cmd format: "[:SOURce]:FREQuency:<mode>?"
        * Short cmd format: ":FREQ:<mode>?"
        * <mode> value:
            * CW | FIXed
        * Queries the output frequency of the specified frequency mode of the generator.
        """
        cmd = ":FREQ:{}?".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        print(float(data))
        return float(data)

    def vsg_device_set_frequency_mode(self, mode: str):
        """
        * Full cmd format: "[:SOURce]:FREQuency:MODE CW|FIXed|LIST"
        * Short cmd format: ":FREQ:MODE CW|FIXed|LIST"
        * mode:
            * CW | FIXed | LIST
            * CW and FIXed :
                * These choices are synonymous with one another and
                    stops a frequency sweep, allowing the Keysight MXG to
                    operate at a set frequency. Refer to the
                    :FREQuency[:CW] command for setting the frequency in
                    the CW mode and to the :FREQuency:FIXed command
                    for setting the frequency in the FIXed mode.
            * LIST:
                * This choice selects the swept frequency mode. If sweep
                    triggering is set to immediate along with continuous
                    sweep mode, executing the command starts the LIST or
                    STEP frequency sweep.
        * This command sets the frequency mode of the signal generator to CW or swept.
        * NOTE:
            * To perform a frequency and amplitude sweep, you must also select LIST as the power mode. See
                the :MODE command for selecting the list mode for an amplitude sweep.
            * :MODE command see:
                * def vsg_device_set_signal_generator_power_mode()
        """
        cmd = ":FREQ:MODE {}".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        # self.device_check_operation_complete()

    def vsg_device_query_frequency_mode(self):
        """
        * Full cmd format: "[:SOURce]:FREQuency:MODE?"
        * Short cmd format: ":FREQ:MODE?"
        * Query the frequency mode of the signal generator.
        """
        cmd = ":FREQ:MODE?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_signal_generator_power_mode(self, mode: str):
        """
        * Full cmd format: "[:SOURce]:POWer:MODE <mode>"
        * Short cmd format: ":POWer:MODE <mode>"
        * <mode>:
            * FIXed:
                * This choice stops a power sweep, allowing the signal
                    generator to operate at a fixed power level. Refer to the
                    [:LEVel][:IMMediate][:AMPLitude] command for setting
                    the output power level.
            * LIST
                * This choice selects the swept power mode. If sweep
                    triggering is set to immediate along with continuous
                    sweep mode, executing the command starts the LIST or
                    STEP power sweep.
        * This command sets the signal generator power mode to fixed or swept.
        * NOTE:
            * To perform a frequency and amplitude sweep, you must also select LIST as the frequency mode.
                See also the :FREQuency:MODE command for selecting the list mode for a frequency sweep.
            * :FREQuency:MODE command See:
                def vsg_device_set_frequency_mode()
        """
        cmd = ":POWer:MODE {}".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        # self.device_check_operation_complete()

    def vsg_device_query_signal_generator_power_mode(self):
        """
        * Full cmd format: "[:SOURce]:POWer:MODE?"
        * Short cmd format: ":POWer:MODE?"
        * This command query the signal generator power mode.
        """
        cmd = ":POWer:MODE?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_query_rf_output_power(self):
        """
        * Full cmd format: "[:SOURce]:POWer[:LEVel][:IMMediate][:AMPLitude]?"
        """
        cmd = ":POWer?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(float(data))
        return float(data)

    def vsg_device_set_rf_output_power(self, value: int or float, unit: str):
        """
        * Full cmd format: "[:SOURce]:POWer[:LEVel][:IMMediate][:AMPLitude] <value><unit>"
        * Short cmd format: ":POWer <value><unit>"
        * <unit>:
            * dBm | dBuV | dBuVemf | mV | uV | mVemf | uVemf
        * This command sets the RF output power.
        """
        cmd = ":POWer:LEVel {}{}".format(value, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_rf_output_power_unit(self):
        """
        * Full cmd format: ":UNIT:POWer?"
        """
        cmd = ":UNIT:POWer?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_rf_output_power_unit(self, unit: str):
        """
        * Full cmd format: ":UNIT:POWer <unit>"
        * <unit>:
            * DBM | DBUV | DBUVEMF | V | VEMF | DB
        * This command terminates an amplitude value in the selected unit of measure.
            If the amplitude reference state is set to on, the query returns units expressed
            in dB and the dB choice will be displayed. Setting any other unit will cause a
            setting conflict error stating that the amplitude reference state must be set to
            off. Refer to, :REFerence:STATe command for more information.
        """
        cmd = ":UNIT:POWer {}".format(unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_set_rf_output_state(self, state: str or int):
        """
        * Full cmd format: ":OUTPut[:STATe] <state>"
        * Short cmd format: ":OUTP <state>"
        * <state>:
            * ON | 1
            * OFF | 0
        * This command enables or disables the RF output.
        * Key Entry: --> RF On/Off
        """
        cmd = ":OUTP {}".format(state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_rf_output_state(self):
        """
        * Full cmd format: ":OUTPut[:STATe]?"
        * Short cmd format: ":OUTP?"
        * Query the RF output state.
        """
        cmd = ":OUTP?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_rf_output_modulation_state(self, state: str or int):
        """
        * Full cmd format: ":OUTPut:MODulation[:STATe] <state>"
        * Short cmd format: ":OUTP:MOD <state>"
        * <state>:
            * ON | 1
            * OFF | 0
        * This command enables or disables the modulation of the RF output with the
            currently active modulation type(s).
        * Key Entry: --> Mod On/Off
        """
        cmd = ":OUTP:MOD {}".format(state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_rf_output_modulation_state(self):
        """
        * Full cmd format: ":OUTPut:MODulation[:STATe]?"
        * Short cmd format: ":OUTP:MOD?"
        * Query the modulation of the RF output with the currently active modulation type(s).
        """
        cmd = ":OUTP:MOD?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_query_sweep_type(self):
        """
        * Full cmd format: "[:SOURce]:LIST:TYPE?"
        * Short cmd format: ":LIST:TYPE?"
        * Query the type of sweep.
        """
        cmd = ":LIST:TYPE?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_sweep_type(self, value: str):
        """
        * Full cmd format: "[:SOURce]:LIST:TYPE <value>"
        * Short cmd format: ":LIST:TYPE <value>"
        * <value>:
            * LIST --> This type of sweep has arbitrary frequencies and amplitudes.
            * STEP --> This type of sweep has equally spaced frequencies and amplitudes.
        * This command toggles between the two types of sweep.
        """
        cmd = ":LIST:TYPE {}".format(value)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_set_current_sweep_operate_mode(self, mode: str):
        """
        * Full cmd format: "[:SOURce]:LIST:MODE <mode>"
        * Short cmd format: ":LIST:MODE <mode>"
        * <mode>:
            * AUTO:
                * This choice enables the selected sweep type to perform a sweep of all points.
            * MANual:
                * This choice enables you to select a single sweep point.
                    The selected point controls the frequency and/or
                    amplitude according to the sweep type. Refer to the
                    :LIST:MANual command for selecting a sweep point.
        * This command sets the operating mode for the current list or step sweep.
            * 该命令设置当前列表或步进扫描的操作模式。
        * Key Entry: --> Manual Mode Off On
        """
        cmd = ":LIST:MODE {}".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_current_sweep_operate_mode(self):
        """
        * Full cmd format: "[:SOURce]:LIST:MODE?"
        * Short cmd format: ":LIST:MODE?"
        * This command query the operating mode for the current list or step sweep.
        """
        cmd = ":LIST:MODE?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_sweep_point_dwell_time_type(self, value: str):
        """
        * Full cmd format: "[:SOURce]:LIST:DWEL1:TYPE <value>"
        * Short cmd format: ":LIST:DWEL1:TYPE <value>"
        * <value>:
            * LIST:
                * This choice selects the dwell times from the list sweep.
                    Refer to the :LIST:DWELl command for setting the list dwell points.
            * STEP:
                * This choice selects the dwell time from the step sweep.
                    Refer to the :SWEep:DWELl command for setting the step dwell.
        * This command toggles the dwell time for the list sweep points between the
            values defined in the list sweep and the value for the step sweep.
        * Key Entry: --> Dwell Type List Step
        """
        cmd = ":LIST:DWEL1:TYPE {}".format(value)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_sweep_point_dwell_time_type(self):
        """
        * Full cmd format: "[:SOURce]:LIST:DWEL1:TYPE?"
        * Short cmd format: ":LIST:DWEL1:TYPE?"
        * This command query the dwell time for the list sweep points between the
            values defined in the list sweep and the value for the step sweep.
        """
        cmd = ":LIST:DWEL1:TYPE?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_step_sweep_start_frequency(self, value: int or float, unit: str):
        """
        * Full cmd format: "[:SOURce]:FREQuency:STARt <value><unit>"
        * Short cmd format: ":FREQ:STAR <value><unit>"
        * <unit>:
            * Hz | KHz | MHz | GHz
        * This command sets the first frequency point in a step sweep.
        """
        cmd = ":FREQ:STAR {}{}".format(value, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_step_sweep_start_frequency(self):
        """
        * Full cmd format: "[:SOURce]:FREQuency:STARt?"
        * Short cmd format: ":FREQ:STAR?"
        * <unit>:
            * Hz | KHz | MHz | GHz
        * This command query the first frequency point in a step sweep.
        """
        cmd = ":FREQ:STAR?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def vsg_device_set_step_sweep_stop_frequency(self, value: int or float, unit: str):
        """
        * Full cmd format: "[:SOURce]:FREQuency:STOP <value><unit>"
        * Short cmd format: ":FREQ:STOP <value><unit>"
        * <unit>:
            * Hz | KHz | MHz | GHz
        * This command sets the last frequency point in a step sweep.
        """
        cmd = ":FREQ:STOP {}{}".format(value, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_step_sweep_stop_frequency(self):
        """
        * Full cmd format: "[:SOURce]:FREQuency:STOP?"
        * Short cmd format: ":FREQ:STOP?"
        * <unit>:
            * Hz | KHz | MHz | GHz
        * This command query the last frequency point in a step sweep.
        """
        cmd = ":FREQ:STOP?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_sweep_points(self, value: int):
        """
        * Full cmd format: "[:SOURce]:SWEep:POINts <value>"
        * Short cmd format: ":SWEep:POINts <value>"
        * <value>:
            * range: 2 to 65535
        * This command defines the number of step sweep points.
        """
        cmd = ":SWEep:POINts {}".format(value)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_sweep_points(self):
        """
        * Full cmd format: "[:SOURce]:SWEep:POINts?"
        * Short cmd format: ":SWEep:POINts?"
        * This command query the number of step sweep points.
        """
        cmd = ":SWEep:POINts?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_select_sweep_repeat_single_cont(self, value: str or int):
        """
        * Full cmd format: ":INITiate:CONTinuous[:ALL] <value>"
        * Short cmd format: ":INIT:CONT <value>"
        * <value>:
            * ON | 1
                * This choice selects continuous sweep where, after the
                    completion of the previous sweep, the current sweep
                    will restart automatically or wait until the appropriate
                    trigger source is received.
            * OFF | 0
                * This choice selects a single sweep. Refer to
                    :INITiate[:IMMediate][:ALL] for single sweep triggering
                    information.
        * This command selects either a continuous or single list or step sweep.
        * Key Entry: --> Sweep Repeat Single Cont
        * Remarks: --> Execution of this command will not affect a sweep in progress.
        """
        cmd = ":INIT:CONT {}".format(value)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_select_sweep_repeat_single_cont(self):
        """
        * Full cmd format: ":INITiate:CONTinuous[:ALL]?"
        * Short cmd format: ":INIT:CONT?"
        * This command query selects either a continuous or single list or step sweep.
        """
        cmd = ":INIT:CONT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_and_start_single_sweep(self):
        """
        * Full cmd format: ":INITiate[:IMMediate][:ALL]"
        * Short cmd format: ":INIT"
        * This command either sets or sets and starts a single List or Step sweep, depending on the trigger type.
            The command performs the following:
            * arms a single sweep when BUS, EXTernal, or KEY is the trigger source selection
            * arms and starts a single sweep when IMMediate is the trigger source selection
        * Key Entry: --> Single Sweep
        * This command is ignored if a sweep is in progress.
            See :INITiate:CONTinuous[:ALL] command for setting continuous or single sweep.
            See :TRIGger[:SEQuence]:SOURce command to select the trigger source.
        """
        cmd = ":INIT"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        # self.device_check_operation_complete()

    def vsg_device_set_sweep_trigger_source(self, source: str):
        """
        * Full cmd format: ":TRIGger[:SEQuence]:SOURce <source>"
        * Short cmd format: ":TRIG:SOUR <source>"
        * <source>:
            * BUS | IMMediate | EXTernal | INTernal | KEY | TIMer | MANual
            * BUS:
                * This choice enables GPIB triggering using the *TRG or GET command.
                    The *TRG SCPI command can be used with any combination of GPIB, LAN, or USB.
                    The GET command requires USB, GPIB, or LAN–VXI–11.
            * IMMediate:
                * This choice enables immediate triggering of the sweep event.
            * EXTernal:
                * This choice enables the triggering of a sweep event by an externally applied signal at the TRIG 1,
                    TRIG 2 or PULSE connector (see :TRIGger:EXTernal:SOURce).
            * INTernal:
                * This choice enables the triggering of a sweep event by an internal Pulse Video or Pulse Sync signal
                    (see :TRIGger:INTernal:SOURce).
            * KEY:
                * This choice enables triggering through front panel interaction by pressing the Trigger key.
            * TIMer:
                * This choice enables the sweep trigger timer.
            * MANual:
                * This choice enables manual sweep triggering.
        * This command sets the sweep trigger source for a list or step sweep.
        * Remarks: --> The wait for the BUS, EXTernal, or KEY trigger can be bypassed by sending
            the :TRIGger[:SEQuence][:IMMediate] command.
        """
        cmd = ":TRIG:SOUR {}".format(source)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_sweep_trigger_source(self):
        """
        * Full cmd format: ":TRIGger[:SEQuence]:SOURce?"
        * Short cmd format: ":TRIG:SOUR?"
        * This command query the sweep trigger source for a list or step sweep.
        """
        cmd = ":TRIG:SOUR?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_set_sweep_event_point_trigger_source(self, source: str):
        """
        * Full cmd format: "[:SOURce]:LIST:TRIGger:SOURce <source>"
        * Short cmd format: ":LIST:TRIG:SOUR <source>"
        * <source>:
            * BUS | IMMediate | EXTernal | INTernal | KEY | TIMer | MANual
            * BUS:
                * This choice enables GPIB triggering using the *TRG or GET command,
                    or LAN and USB triggering using the *TRG command.
            * IMMediate:
                * This choice enables immediate triggering of the sweep event.
            * EXTernal:
                * This choice enables the triggering of a sweep event by an externally applied signal
                    at the TRIGGER IN connector.
            * INTernal:
                * This choice enables the triggering of a sweep event by an internal Pulse Video or Pulse Sync signal.
            * KEY:
                * This choice enables triggering by pressing the front-panel Trigger key.
            * TIMer:
                * This choice enables the trigger timer.
            * MANual:
                * This choice enables manual sweep triggering.
        * This command sets the point trigger source for a list or step sweep event.
        """
        cmd = ":LIST:TRIG:SOUR {}".format(source)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        # self.device_check_operation_complete()

    def vsg_device_query_sweep_event_point_trigger_source(self):
        """
        * Full cmd format: "[:SOURce]:LIST:TRIGger:SOURce?"
        * Short cmd format: ":LIST:TRIG:SOUR?"
        * This command query the point trigger source for a list or step sweep event.
        """
        cmd = ":LIST:TRIG:SOUR?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_execute_remote_stepped_sweep(self):
        """
        * Full cmd format: "*TRG"
        * The Trigger (TRG) command triggers the device if BUS is the selected trigger
            source, otherwise, *TRG is ignored.
        """
        cmd = "*TRG"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        # self.device_check_operation_complete()

    def vsg_device_real_time_custom_modulation_set_enable_state(self, state: str or int):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom[:STATe] <state>"
        * Short cmd format: ":RAD:CUST <state>"
        * <state>:
            * ON | 1
            * OFF | 0
        * 功能：该命令用于启用或禁用实时自定义调制。
        """
        cmd = ":RADio:CUSTom {}".format(state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_enable_state(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom[:STATe]?"
        * Short cmd format: ":RAD:CUST?"
        * 功能：查询实时自定义调制状态。
        """
        cmd = ":RADio:CUSTom?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_select_quick_setup_standard(self, standard: str):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:STANdard <standard>"
        * Short cmd format: ":RAD:CUST:STAN <standard>"
        * <standard>:
            * NONE
            * AC4Fm
            * ACQPsk
            * BLUEtooth
            * CDPD
        * 功能：该命令用于选择快速设置标准。
        """
        cmd = ":RADio:CUSTom:STANdard {}".format(standard)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_quick_setup_standard(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:STANdard?"
        * Short cmd format: ":RAD:CUST:STAN?"
        * 功能：查询快速设置标准。
        """
        cmd = ":RADio:CUSTom:STANdard?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_set_mod_type(self, mod_type: str):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:MODulation:[TYPE] <mod_type>"
        * Short cmd format: ":RAD:CUST:MOD <mod_type>"
        * <mod_type>:
            * ASK
            * BPSK|QPSK|UQPSk|IS95QPSK|GRAYQPSK|OQPSK|IS95OQPSK|P4DQPSK|PSK8|PSK16|D8PSK|EDGE
            * MSK
            * FSK2|FSK4|FSK8|FSK16|C4FM
            * QAM4|QAM16|QAM32|QAM64|QAM128|QAM256|QAM1024|VSAQAM16|VSAQAM32|VSAQAM64|VSAQAM128|VSAQAM256|VSAQAM512|VSAQAM1024
            * UIQ
            * UFSK
        * 功能：该命令用于设置调制类型。
        """
        cmd = ":RADio:CUSTom:MODulation {}".format(mod_type)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_mod_type(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:MODulation:[TYPE]?"
        * Short cmd format: ":RAD:CUST:MOD?"
        * 功能：查询调制类型。
        """
        cmd = ":RADio:CUSTom:MODulation?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_set_symmetric_fsk_freq_deviation(self, freq_dev: int or float):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:MODulation:FSK[:DEViation] <freq_dev>"
        * Short cmd format: ":RAD:CUST:MOD:FSK <freq_dev>"
        * <freq_dev>:
            * range: 0 ~ 2E7
            * unit：default Hz
        * 功能：该命令用于设置对称 FSK 频率偏移。单位默认Hz。
        """
        cmd = ":RADio:CUSTom:MODulation:FSK {}".format(freq_dev)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_symmetric_fsk_freq_deviation(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:MODulation:FSK[:DEViation]?"
        * Short cmd format: ":RAD:CUST:MOD:FSK?"
        * 功能：查询对称 FSK 频率偏移。
        """
        cmd = ":RADio:CUSTom:MODulation:FSK?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_select_filter_type(self, filter_type: str):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:FILTer <filter_type>"
        * Short cmd format: ":RAD:CUST:FIL <filter_type>"
        * <filter_type>:
            * RNYQuist|NYQuist|GAUSsian|RECTangle|IS95| IS95_EQ|IS95_MOD|IS95_MOD_EQ|AC4Fm|UGGaussian|"<user FIR>"
        * 功能：该命令用于选择滤波器类型。
        """
        cmd = ":RADio:CUSTom:FILTer {}".format(filter_type)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_filter_type(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:FILTer?"
        * Short cmd format: ":RAD:CUST:FIL?"
        * 功能：查询滤波器类型。
        """
        cmd = ":RADio:CUSTom:FILTer?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_set_gaussian_filter_bbt(self, bbt: int or float):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:BBT <bbt>"
        * Short cmd format: ":RAD:CUST:BBT <bbt>"
        * <bbt>:
            * range: 0.100 ~ 1.000
        * 功能：该命令用于设置高斯滤波器的BBT。
            * 该命令只有在选择高斯滤波器后才有效。它对其他类型的过滤器没有影响。
        """
        cmd = ":RADio:CUSTom:BBT {}".format(bbt)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_gaussian_filter_bbt(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:BBT?"
        * Short cmd format: ":RAD:CUST:BBT?"
        * 功能：查询高斯滤波器的BBT。
        """
        cmd = ":RADio:CUSTom:BBT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_set_transmission_symbol_rate(self, symbol_rate: int or float):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:SRATe <symbol_rate>"
        * Short cmd format: ":RAD:CUST:SRAT <symbol_rate>"
        * <symbol_rate>:
            * range:
                * Min Symbol Rate： 1 sps
                * N5172B Opt 653 Max Symbol Rate：37.5 Msps
                * N5172B Opt 655 Max Symbol Rate：57 Msps
            * unit: default sps
        * 功能：该命令用于设置传输符号率。
        """
        cmd = ":RADio:CUSTom:SRATe {}".format(symbol_rate)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_transmission_symbol_rate(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:SRATe?"
        * Short cmd format: ":RAD:CUST:SRAT?"
        * 功能：查询传输符号率。
        """
        cmd = ":RADio:CUSTom:SRATe?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data

    def vsg_device_real_time_custom_modulation_set_unframed_transmission_data_pattern(self, data_pattern: str):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:DATA <data_pattern>"
        * Short cmd format: ":RAD:CUST:DATA <data_pattern>"
        * <data_pattern>:
            * PN9|PN11|PN15|PN20|PN23|FIX4|"<file name>"| EXT|P4|P8|P16|P32|P64|PRAM
        * 功能：该命令用于设置无帧传输数据模式。
        """
        cmd = ":RADio:CUSTom:DATA {}".format(data_pattern)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def vsg_device_query_real_time_custom_modulation_unframed_transmission_data_pattern(self):
        """
        * Full cmd format: "[:SOURce]:RADio:CUSTom:DATA?"
        * Short cmd format: ":RAD:CUST:DATA?"
        * 功能：查询无帧传输数据模式。
        """
        cmd = ":RADio:CUSTom:DATA?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        # print(data)
        return data


