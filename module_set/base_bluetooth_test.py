# -*- coding: utf-8 -*-
import time
import platform
import os
import threading
from datetime import datetime
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import DeviceRun
from module_set.handle_report_class import HandleExcelReport
from module_set.test_params import sagitta_symbol_rate_chg, sagitta_signal_channel_chg
from module_set.test_params import sagitta_scode_rate_chg, arb_file_snr_chg, arb_file_str_scode_rate, arb_file_bk, arb_file_fps, arb_file_ptype

class BaseBluetoothTest(DeviceRun):
    """蓝牙测试基类，提供通用功能"""

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data

        self.register_data_history = []  # 存储寄存器数据历史
        self.test_running = False  # 控制测试运行状态

        self.case_func = None   # 需在子类具体用例中传递参数
        self.rf_board_init_state = False

        #  {'data_len': 6, 'sym_rate': 0, 's_code_en': 1, 'scode_rate': 2, 'snr': 100, 'signal_ch': 0, 'inf_fre_offset_point': 0, 'inf_fre_calc_op': 1, 'inf_fre_offset': 0}
        self.cur_case = dict() # 需在子类具体用例中传递参数，该参数暂时用于a2 - a1 < n时，n值由data_len决定

        # 设备配置(需在子类具体用例中配置具体参数)
        self.cmw_config = dict()
        self.vsg_config = dict()

        # 报告配置
        self.report_config = {
            "sheet_name_template": "{data_len}byte_{case_func}",
            # "error_sheet_name": "Error_Log"
        }

    def initialize_test_environment(self, test_case_file=None):
        """初始化测试环境，包括设备连接和报告文件创建

        Args:
            test_case_file: 测试用例文件路径，如果不提供则使用当前文件
        """
        self.write_info_log("=== 初始化测试环境 ===")

        # 初始化设备和校验设备连接
        self.write_info_log("正在初始化设备连接...")
        ret, error = self.init_device_data_and_check_connection_correct()
        if not ret:
            self.write_error_log(f"设备初始化失败: {error}")
            return False, error

        # 创建报告文件
        self.write_info_log("正在创建测试报告...")
        # 如果没有提供测试用例文件，则使用当前文件
        tc_file = test_case_file if test_case_file else __file__
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=tc_file)
        if not ret:
            self.write_error_log(f"创建报告文件失败: {error}")
            return False, error

        self.tc_report_path = report_path_info["tc_report_path"]
        self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        self.write_info_log(f"测试报告创建成功: {self.tc_report_path}")

        return True, None

    def cleanup_test_environment(self):
        """清理测试环境"""
        self.write_info_log("=== 清理测试环境 ===")
        try:
            self.disconnection_device()
            self.write_info_log("设备连接已断开")
            return True, None
        except Exception as e:
            error_msg = f"清理测试环境失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def setup_cmw_generator(self, test_fre):
        """配置CMW信号发生器参数"""
        self.write_info_log("开始配置CMW信号发生器...")
        try:
            self.cmw_api.cmw_func_gprf_generator_turn_off_status()
            self.cmw_api.cmw_device_gprf_generator_set_signal_path(
                self.cmw_config["signal_path"],
                self.cmw_config["tx_path"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_signal_baseband_mode(
                self.cmw_config["baseband_mode"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_arb_file_repetition_mode(
                self.cmw_config["repetition_mode"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_rms_level(
                self.cmw_config["rms_level"]
            )
            self.cmw_api.cmw_device_gprf_generator_set_unmod_rf_carrier_frequency(
                test_fre, "MHz"
            )
            self.write_info_log(f"CMW信号发生器配置完成，频率: {test_fre}MHz")
            return True, None
        except Exception as e:
            error_msg = f"CMW信号发生器配置失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def sagitta_test_create_arb_file_path_from_params(
            self, sym_rate: int, scode_rate: float, snr: int, data_len: int, test_func: str):
        """
        根据参数生成 ARB 文件路径
        * sym_rate: 0-1M  1-2M  2-4M
        * scode_rate: 2-2/8  3-3/8  4-4/8  5-5/8  6-6/8  7-7/8
        * snr: 1-0dB  2-1dB  3-2dB  4-3dB  5-4dB  6-5dB
        * data_len: 数据长度，单位为 byte
        * test_func: 测试功能，eg: Gauss
        """
        error = None
        if sym_rate not in sagitta_symbol_rate_chg.keys():
            error = "sym_rate 参数错误"
            return False, error
        if scode_rate not in sagitta_scode_rate_chg.keys():
            error = "scode_rate 参数错误"
            return False, error
        if snr not in arb_file_snr_chg.keys():
            error = "snr_code 参数错误"
            return False, error

        cur_sym_rate = sagitta_symbol_rate_chg[sym_rate]
        cur_scode_rate = sagitta_scode_rate_chg[scode_rate]
        cur_snr = arb_file_snr_chg[snr]

        if test_func == "Gauss":
            fix_path = os.path.join("D:", "sle_stream", "test")
            first_path = r"{}byte_{}".format(data_len, test_func)
            second_path = r"{}M".format(cur_sym_rate)
            third_path = "R{}_polar{}".format(
                cur_sym_rate,
                arb_file_str_scode_rate[cur_scode_rate])
            if data_len == 6:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    cur_snr)
            else:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_{}byte_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    data_len,
                    cur_snr)
            arb_file_path = os.path.join(fix_path, first_path, second_path, third_path, file_name)
            print(arb_file_path)
            if platform.system() in ["Linux", "Darwin"]:
                arb_file_path = arb_file_path.replace("/", "\\")
            return True, arb_file_path
        elif test_func == "Sensitivity" or test_func == "CI":
            fix_path = os.path.join("D:", "sle_stream", "test")
            first_path = r"{}byte_{}".format(data_len, "Sensitivity")
            second_path = r"{}M".format(cur_sym_rate)
            if data_len == 6:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_addsine_{}bk_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    cur_snr)
            else:
                file_name = "qpsk_{}Rs_24Fs_{}fps_PType{}_seed0_{}bk_{}byte_sym{}db_grdc.wv".format(
                    cur_sym_rate,
                    arb_file_fps[data_len][cur_sym_rate],
                    arb_file_ptype[cur_scode_rate],
                    arb_file_bk[data_len][cur_scode_rate],
                    data_len,
                    cur_snr)
            arb_file_path = os.path.join(fix_path, first_path, second_path, file_name)
            return True, arb_file_path
        else:
            error = "test_func 参数错误"
            return False, error

    def sagitta_test_load_arb_file(self, test_params):
        """加载ARB文件并验证"""
        self.write_info_log("开始加载ARB文件...")
        try:
            ret, arb_file_path = self.sagitta_test_create_arb_file_path_from_params(
                sym_rate=test_params["sym_rate"],
                scode_rate=test_params["scode_rate"],
                snr=test_params["snr"],
                data_len=test_params["data_len"],
                test_func=self.case_func
            )

            if not ret:
                return False, f"创建ARB文件路径失败: {arb_file_path}"

            ret, error = self.cmw_api.cmw_func_query_gprf_generator_arb_file_is_exist(arb_file_path)
            if not ret:
                return False, f"ARB文件不存在: {error}"

            self.cmw_api.cmw_device_gprf_generator_set_arb_file(arb_file_path)
            loaded_file = self.cmw_api.cmw_device_gprf_generator_query_arb_file()
            self.write_info_log(f"ARB文件加载成功: {loaded_file}")

            return True, None
        except Exception as e:
            error_msg = f"加载ARB文件失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def validate_test_params(self, params):
        """验证测试参数的有效性"""
        try:
            for key, value in params.items():
                if value is None:
                    return False, f"参数 {key} 不能为空"
                if isinstance(value, (list, tuple)) and len(value) == 0:
                    return False, f"参数 {key} 不能为空列表"
            return True, None
        except Exception as e:
            return False, f"参数验证失败: {str(e)}"

    def create_gauss_testcases(self, test_params_config):
        """创建高斯测试用例列表

        Args:
            test_params_config (dict): 高斯测试参数配置，包含以下字段：
                - SYM_RATES: 符号率配置列表 [0, 1, 2]
                - S_CODE_EN: 编码使能配置列表 [0, 1, 2]
                - SCODE_RATES: 编码率配置列表
                - SNRS: 信噪比配置列表
                - DATA_LEN: 数据长度配置列表
                - MAX_TEST_CASES: 最大测试用例数

        Returns:
            list: 生成的测试用例列表，每个用例包含完整的测试参数
        """
        self.write_info_log("=== 生成高斯测试用例 ===")

        # 验证测试参数配置
        ret, error = self.validate_test_params(test_params_config)
        if not ret:
            self.write_error_log(f"测试参数配置无效: {error}")
            return []

        try:
            # 从配置中获取参数
            params = test_params_config
            self.write_info_log("高斯测试参数配置:")
            for key, value in params.items():
                if key != "MAX_TEST_CASES":
                    self.write_info_log(f"  {key}: {value}")

            # 生成测试用例
            testcase_list = []
            for dl in params["DATA_LEN"]:
                for sr in params["SYM_RATES"]:
                    for sce in params["S_CODE_EN"]:
                        for scr in params["SCODE_RATES"]:
                            for snr in params["SNRS"]:
                                # 获取对应的信号通道
                                channels = sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]]
                                for ch in channels:
                                    testcase = {
                                        "data_len": dl,
                                        "sym_rate": sr,
                                        "s_code_en": sce,
                                        "scode_rate": scr,
                                        "snr": snr,
                                        "signal_ch": ch
                                    }
                                    testcase_list.append(testcase)

            # 限制测试用例数量
            max_cases = params["MAX_TEST_CASES"]
            if len(testcase_list) > max_cases:
                self.write_info_log(f"测试用例数量({len(testcase_list)})超过限制({max_cases})，将只执行前{max_cases}个用例")
                testcase_list = testcase_list[:max_cases]

            self.write_info_log(f"成功生成 {len(testcase_list)} 个高斯测试用例")
            return testcase_list

        except Exception as e:
            error_msg = f"生成高斯测试用例失败: {str(e)}"
            self.write_error_log(error_msg)
            return []

    def create_sensitivity_testcases(self, test_params_config):
        """创建灵敏度测试用例列表

        Args:
            test_params_config (dict): 灵敏度测试参数配置，包含以下字段：
                - SYM_RATES: 符号率配置列表 [0, 1, 2]
                - S_CODE_EN: 编码使能配置列表 [0, 1, 2]
                - SCODE_RATES: 编码率配置列表
                - SNRS: 信噪比配置列表
                - DATA_LEN: 数据长度配置列表
                - MAX_TEST_CASES: 最大测试用例数

        Returns:
            list: 生成的测试用例列表，每个用例包含完整的测试参数
        """
        self.write_info_log("=== 生成灵敏度测试用例 ===")

        # 验证测试参数配置
        ret, error = self.validate_test_params(test_params_config)
        if not ret:
            self.write_error_log(f"测试参数配置无效: {error}")
            return []

        try:
            # 从配置中获取参数
            params = test_params_config
            self.write_info_log("灵敏度测试参数配置:")
            for key, value in params.items():
                if key != "MAX_TEST_CASES":
                    self.write_info_log(f"  {key}: {value}")

            # 生成测试用例
            testcase_list = []
            for dl in params["DATA_LEN"]:
                for sr in params["SYM_RATES"]:
                    for sce in params["S_CODE_EN"]:
                        for scr in params["SCODE_RATES"]:
                            for snr in params["SNRS"]:
                                # 获取对应的信号通道
                                channels = sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]]
                                for ch in channels:
                                    testcase = {
                                        "data_len": dl,
                                        "sym_rate": sr,
                                        "s_code_en": sce,
                                        "scode_rate": scr,
                                        "snr": snr,
                                        "signal_ch": ch
                                    }
                                    testcase_list.append(testcase)

            # 限制测试用例数量
            max_cases = params["MAX_TEST_CASES"]
            if len(testcase_list) > max_cases:
                self.write_info_log(f"测试用例数量({len(testcase_list)})超过限制({max_cases})，将只执行前{max_cases}个用例")
                testcase_list = testcase_list[:max_cases]

            self.write_info_log(f"成功生成 {len(testcase_list)} 个灵敏度测试用例")
            return testcase_list

        except Exception as e:
            error_msg = f"生成灵敏度测试用例失败: {str(e)}"
            self.write_error_log(error_msg)
            return []

    def run(self, test_case_file=None):
        """主运行函数

        Args:
            test_case_file: 测试用例文件路径，用于报告文件创建。
                          如果不提供，将使用当前文件路径
        """
        try:
            # 初始化测试环境
            ret, error = self.initialize_test_environment(test_case_file)
            if not ret:
                return ret, error

            # 执行测试流程
            self.edit_cur_testcase_test_process()

            # 清理测试环境
            ret, error = self.cleanup_test_environment()
            if not ret:
                return ret, error

            self.write_info_log("=== 测试完成 ===")
            return True, None

        except Exception as e:
            error_msg = f"测试执行过程中发生异常: {str(e)}"
            # raise Exception(error_msg)
            self.write_error_log(error_msg)
            # 确保在发生异常时也清理环境
            self.cleanup_test_environment()
            return False, error_msg

    def edit_cur_testcase_test_process(self):
        """主测试流程，需要子类实现"""
        raise NotImplementedError

    def run_single_test(self, tc, report_sheet_name):
        """执行单次测试，需要子类实现"""
        raise NotImplementedError

    def get_register_data_history(self):
        """获取寄存器数据历史记录"""
        return self.register_data_history.copy()

    def calc_sagitta_dut_guass_per(self, old_data: dict, cur_data: dict):
        a1 = old_data["rx_ok"]
        b1 = old_data["crc_err"]
        c1 = old_data["len_err"]
        d1 = old_data["sync_err"]
        a2 = cur_data["rx_ok"]
        b2 = cur_data["crc_err"]
        c2 = cur_data["len_err"]
        d2 = cur_data["sync_err"]
        if a1 == a2 or a1 + b1 + c1 + d1 == a2 + b2 + c2 + d2:
            error = "rx_ok same or total cnt same, can't calc per!"
            print(error)
            # return False, error
            return True, 100

        try:
            per = float("{:.2f}".format((1 - (a2 - a1) / (a2 + b2 + c2 + d2 - a1 - b1 - c1 - d1)) * 100))
            return True, per
        except Exception as e:
            print(str(e))
            return False, str(e)

    def get_polar_err_from_register_data(self, register_data: list):
        """
        从寄存器数据中获取极化误差
        * register_data:
            * list: [{'timestamp': '2023-09-11 10:00:00.000', 'reg_addr': '0xb40211a8', 'h_bit': 16, 'l_bit': 0, 'value': '3.0518e-03'}, ...]
        """
        polar_err_list = list()
        for data in register_data:
            polar_err_list.append(data["value"])

        min_value = "{:.4e}".format(min((float(x) for x in polar_err_list)))
        max_value = "{:.4e}".format(max((float(x) for x in polar_err_list)))
        # print("{}~{}".format(min_value, max_value))
        return "{}~{}".format(min_value, max_value)

    def sagitta_dut_get_per_and_polar_err_data(self, reg_addr: str, h_bit: int = 16, l_bit: int = 0):
        """改进的高斯测试数据解析函数 - 合并数据采集步骤"""
        try:
            # 1. 初始化和清理
            self.register_data_history.clear()
            self.test_running = True  # 设置测试运行状态

            # 2. 参数验证
            if not all([reg_addr, h_bit is not None, l_bit is not None]):
                return False, "Invalid register parameters"

            # 3. 第一次数据采集前的等待（简化为直接等待）
            self.write_info_log("Waiting 5s for first data collection...")
            time.sleep(5)  # 等待5秒

            # 4. 第一次数据采集
            self.write_debug_log("Collecting first data...")
            ret, first_data = self._collect_data_with_retry()
            if not ret:
                return False, first_data
            self.write_debug_log("first data: {}".format(first_data))

            if first_data['rx_ok'] == 0 and first_data['crc_err'] == 0 and first_data['len_err'] == 0 and first_data['sync_err'] == 0:
                return False, "No data received in first collection"

            # 5. 合并的数据采集步骤：同时进行第二次数据收集和寄存器数据采集
            ret, second_data = self._collect_combined_data(first_data, reg_addr, h_bit, l_bit)
            if not ret:
                return False, second_data

            # 6. 计算PER值
            self.write_debug_log("Calculating PER...")
            ret_per, per = self.calc_sagitta_dut_guass_per(first_data, second_data)
            if not ret_per:
                return False, "Failed to calculate PER"

            # 7. 停止测试并返回结果
            self.test_running = False  # 停止测试
            self.uart_handle.interact_with_sagitta_dut_end_test()

            return True, {
                'per': per,
                'first_data': first_data,
                'second_data': second_data,
                'register_data_history': self.register_data_history.copy(),
                'polar_err': self.get_polar_err_from_register_data(self.register_data_history.copy())
            }

        except Exception as e:
            self.write_error_log(f"Error in data collection: {e}")
            return False, str(e)
        finally:
            self.test_running = False  # 确保在任何情况下都停止测试

    def _collect_data_with_retry(self, max_attempts=3):
        """带重试的数据采集方法"""
        for attempt in range(max_attempts):
            try:
                ret, data = self.uart_handle.interact_with_sagitta_dut_read_data()
                if ret:
                    return True, data
                time.sleep(1)
            except Exception as e:
                self.write_error_log(f"Data collection attempt {attempt + 1} failed: {e}")
        return False, "Failed to collect data after all attempts"

    def _collect_combined_data(self, first_data: dict, reg_addr: str, h_bit: int, l_bit: int):
        """
        合并的数据采集方法：同时进行第二次数据收集和寄存器数据采集

        Args:
            first_data: 第一次采集的数据
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数

        Returns:
            tuple: (success, second_data)
        """
        min_fps_chg = {
            6: 1000,
            37: 500,
            255: 100
        }
        rx_diff_min = min_fps_chg.get(self.cur_case["data_len"], 500)
        try:
            self.write_debug_log("开始第二次数据收集和寄存器数据采集流程...")

            # 初始化时间记录
            start_time = time.time()
            last_data_collection_time = start_time
            last_register_collection_time = start_time

            # 采集间隔设置
            data_collection_interval = 5  # 第二次数据收集间隔（5秒）
            register_collection_interval = 2  # 寄存器数据采集间隔（2秒）

            second_data = None

            while self.test_running:
                current_time = time.time()

                # 检查是否需要进行第二次数据收集
                if current_time - last_data_collection_time >= data_collection_interval:
                    self.write_debug_log("正在进行第二次数据收集...")
                    ret, temp_second_data = self.uart_handle.interact_with_sagitta_dut_read_data()
                    self.write_debug_log("temp second data: {}".format(temp_second_data))

                    if ret:
                        # 检查跳出循环的条件
                        rx_diff = temp_second_data['rx_ok'] - first_data['rx_ok']

                        if rx_diff > 10000:
                            self.write_info_log(f"数据采集完成：rx_ok差值({rx_diff}) > 10000")
                            second_data = temp_second_data
                            break
                        elif rx_diff < rx_diff_min:
                            self.write_info_log(f"数据采集完成：rx_ok差值({rx_diff}) < {rx_diff_min}")
                            second_data = temp_second_data
                            break
                        else:
                            self.write_info_log(f"继续采集：rx_ok差值({rx_diff})在范围内，继续等待...")
                            second_data = temp_second_data  # 保存最新数据
                    else:
                        self.write_error_log(f"第二次数据收集失败: {temp_second_data}")

                    last_data_collection_time = current_time

                # 检查是否需要进行寄存器数据采集
                if current_time - last_register_collection_time >= register_collection_interval:
                    try:
                        ret, reg_value = self.uart_handle.interact_with_sagitta_dut_get_register_data(
                            reg_addr, h_bit, l_bit)

                        if ret:
                            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                            register_entry = {
                                'timestamp': timestamp,
                                'reg_addr': reg_addr,
                                'h_bit': h_bit,
                                'l_bit': l_bit,
                                'value': reg_value
                            }
                            self.register_data_history.append(register_entry)
                            self.write_debug_log(f"Register data collected: {register_entry}")
                        else:
                            self.write_error_log(f"Failed to collect register data: {reg_value}")
                    except Exception as e:
                        self.write_error_log(f"Error in register data collection: {e}")

                    last_register_collection_time = current_time

                # 短暂睡眠避免过度占用CPU
                time.sleep(0.1)

                # 超时保护（最多60秒）
                if current_time - start_time > 60:
                    self.write_error_log("合并数据采集超时")
                    break

            if second_data is None:
                return False, "未能收集到有效的第二次数据"

            self.write_info_log("第二次数据收集和寄存器数据采集完成")
            return True, second_data

        except Exception as e:
            self.write_error_log(f"Error in combined data collection: {e}")
            return False, str(e)

    def sagitta_dut_get_gauss_result(self, params: str):

        try:
            # 1. 先确保之前的测试完全结束
            time.sleep(0.5)    # 短暂等待确保线程完全停止

            # 2. 使用更可靠的测试结束方法
            ret, error = self.uart_handle.interact_with_sagitta_dut_end_test()
            if not ret:
                self.write_error_log(f"Failed to ended rf board test: {error}")
                return False, error

            # 3. 配置新的测试参数
            ret = self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
            if not ret:
                error = "Failed to configure test params"
                return False, error
            time.sleep(0.5)

            # 4. 执行测试并获取结果
            return self.sagitta_dut_get_per_and_polar_err_data(reg_addr="0xb40211a8", h_bit=16, l_bit=0)
        except Exception as e:
            return False, str(e)

    def _perform_sensitivity_test_at_level(self, rms_level: float, params: str, reg_addr: str, h_bit: int, l_bit: int):
        """
        在指定RMS电平下执行灵敏度测试

        Args:
            rms_level: RMS电平 (dBm)
            params: 测试参数
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数

        Returns:
            tuple: (success, result)
        """
        try:
            self.cmw_api.cmw_device_gprf_generator_set_rms_level(rms_level)
            time.sleep(1)  # 等待设备稳定

            self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
            time.sleep(0.5)

            return self.sagitta_dut_get_per_and_polar_err_data(reg_addr, h_bit, l_bit)
        except Exception as e:
            return False, f"在RMS电平{rms_level}dBm下测试失败: {str(e)}"

    def _cleanup_test_state(self):
        """
        清理测试状态
        """
        try:
            self.uart_handle.interact_with_sagitta_dut_end_test()
            self.write_info_log("测试状态清理完成")
        except Exception as e:
            self.write_error_log(f"清理测试状态失败: {e}")

    def _validate_sensitivity_test_config(self, test_config: dict) -> tuple:
        """
        验证灵敏度测试配置参数

        Args:
            test_config: 测试配置字典

        Returns:
            tuple: (is_valid, error_message)
        """
        required_keys = ['default_rms_level', 'min_rms_level', 'quick_step', 'slow_step', 'ref_per']

        for key in required_keys:
            if key not in test_config:
                return False, f"缺少必需的配置参数: {key}"

        # 验证参数范围
        if test_config['default_rms_level'] <= test_config['min_rms_level']:
            return False, "默认RMS电平必须大于最小RMS电平"

        if test_config['quick_step'] <= 0 or test_config['slow_step'] <= 0:
            return False, "步进值必须大于0"

        if test_config['ref_per'] <= 0 or test_config['ref_per'] > 100:
            return False, "参考PER阈值必须在0-100%之间"

        return True, None

    def sagitta_dut_get_sensitivity_result(self, params: str):
        try:
            ret, error = self.uart_handle.interact_with_sagitta_dut_end_test()
            if not ret:
                self.write_error_log(f"Failed to ended rf board test: {error}")
                return False, error
            time.sleep(0.5)

            ret = self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
            if not ret:
                error = "Failed to configure test params"
                return False, error
            time.sleep(0.5)

            return self.sagitta_dut_parse_sensitivity_read_data(params=params, reg_addr="0xb40211a8", h_bit=16, l_bit=0)
        except Exception as e:
            return False, str(e)

    def create_ci_testcases(self, test_params_config):

        self.write_info_log("=== 生成C/I测试用例 ===")

        # 验证测试参数配置
        ret, error = self.validate_test_params(test_params_config)
        if not ret:
            self.write_error_log(f"测试参数配置无效: {error}")
            return []

        try:
            # 从配置中获取参数
            params = test_params_config
            self.write_info_log("C/I测试参数配置:")
            for key, value in params.items():
                if key != "MAX_TEST_CASES":
                    self.write_info_log(f"  {key}: {value}")

            # 生成测试用例
            testcase_list = []
            for dl in params["DATA_LEN"]:
                for sr in params["SYM_RATES"]:
                    for sce in params["S_CODE_EN"]:
                        for scr in params["SCODE_RATES"]:
                            for snr in params["SNRS"]:
                                # 获取对应的信号通道
                                channels = sagitta_signal_channel_chg[sagitta_symbol_rate_chg[sr]][:1]
                                for ch in channels:
                                    for point in params["INF_FRE_OFFSET_POINT"]:
                                        for calc_op in params["INF_FRE_CALC_OP"]:
                                            if point == 0 and calc_op == -1:
                                                continue
                                            testcase = {
                                                "data_len": dl,
                                                "sym_rate": sr,
                                                "s_code_en": sce,
                                                "scode_rate": scr,
                                                "snr": snr,
                                                "signal_ch": ch,
                                                "inf_fre_offset_point": point,
                                                "inf_fre_calc_op": calc_op,
                                                "inf_fre_offset": sagitta_symbol_rate_chg[sr] * point * calc_op,
                                            }
                                            testcase_list.append(testcase)
            # 限制测试用例数量
            max_cases = params["MAX_TEST_CASES"]
            if len(testcase_list) > max_cases:
                self.write_info_log(f"测试用例数量({len(testcase_list)})超过限制({max_cases})，将只执行前{max_cases}个用例")
                testcase_list = testcase_list[:max_cases]

            self.write_info_log(f"成功生成 {len(testcase_list)} 个C/I测试用例")
            return testcase_list

        except Exception as e:
            error_msg = f"生成C/I测试用例失败: {str(e)}"
            self.write_error_log(error_msg)
            return []

    def ci_test_setup_vsg_generator(self, inf_fre):
        """配置VSG信号发生器参数"""
        self.write_info_log("开始配置VSG信号发生器...")
        delay = 0.1
        try:
            self.vsg_api.vsg_device_set_preset_all()
            # self.vsg_api.vsg_device_set_rf_output_modulation_state(state="OFF")
            # self.vsg_api.vsg_device_set_rf_output_state(state="OFF")

            # 设置频率模式为FIXED
            self.vsg_api.vsg_device_set_frequency_mode(mode="FIXed")
            time.sleep(delay)
            # 设置具体载波频率
            self.vsg_api.vsg_device_set_output_frequency(value=inf_fre, unit="MHz", mode="FIXed")
            time.sleep(delay)
            # 启用实时自定义调制
            self.vsg_api.vsg_device_real_time_custom_modulation_set_enable_state(state="ON")
            time.sleep(delay)
            # 选择快速设置标准为蓝牙
            # self.vsg_api.vsg_device_real_time_custom_modulation_select_quick_setup_standard(standard="BLUEtooth")
            # time.sleep(delay)
            # 设置滤波器类型为高斯滤波器
            self.vsg_api.vsg_device_real_time_custom_modulation_select_filter_type(filter_type="GAUSsian")
            time.sleep(delay)
            # 设置高斯滤波器的BBT
            self.vsg_api.vsg_device_real_time_custom_modulation_set_gaussian_filter_bbt(
                bbt=self.vsg_config["gaussian_filter_bbt"])
            time.sleep(delay)
            # 设置传输符号率
            self.vsg_api.vsg_device_real_time_custom_modulation_set_transmission_symbol_rate(
                symbol_rate=self.vsg_config["sym_rate"])
            time.sleep(delay)
            # 设置调制类型为FSK2
            self.vsg_api.vsg_device_real_time_custom_modulation_set_mod_type(mod_type="FSK2")
            time.sleep(delay)
            # 设置对称FSK频率偏移
            self.vsg_api.vsg_device_real_time_custom_modulation_set_symmetric_fsk_freq_deviation(
                freq_dev=self.vsg_config["fsk_freq_dev"])
            time.sleep(delay)
            # 设置无帧传输数据模式
            self.vsg_api.vsg_device_real_time_custom_modulation_set_unframed_transmission_data_pattern(
                data_pattern=self.vsg_config["pn_sequence"])
            time.sleep(delay)
            # 设置输出功率
            self.vsg_api.vsg_device_set_rf_output_power(value=self.vsg_config["default_rms_level"], unit="DBM")
            time.sleep(delay)
            # 设置调制状态为ON
            self.vsg_api.vsg_device_set_rf_output_modulation_state(state="ON")
            time.sleep(delay)
            # 设置输出状态为ON
            self.vsg_api.vsg_device_set_rf_output_state(state="ON")
            time.sleep(delay)

            self.write_info_log(f"VSG信号发生器配置完成，频率: {inf_fre}MHz")
            return True, None
        except Exception as e:
            error_msg = f"VSG信号发生器配置失败: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg

    def sagitta_dut_parse_sensitivity_read_data(
            self, params: str, reg_addr: str, h_bit: int = 16, l_bit: int = 0, test_config: dict = None):
        """
        灵敏度测试数据解析函数（优化版本）

        Args:
            params: 测试参数字典，包含以下字段：
                - sym_rate: 符号率
                - signal_ch: 信道
                - s_code_en: 编码使能
                - scode_rate: 编码率
                - snr: 信噪比
                - data_len: 数据长度
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数
            test_config: 测试配置字典，包含以下可选字段：
                - default_rms_level: 默认RMS电平 (dBm)，默认-90
                - min_rms_level: 最小RMS电平 (dBm)，默认-120
                - quick_step: 快速步进值 (dB)，默认5
                - slow_step: 慢速步进值 (dB)，默认1
                - ref_per: 参考PER阈值 (%)，默认10

        Returns:
            tuple: (success, result)
                success: bool, 是否成功
                result: dict 或 str, 成功时返回结果字典，失败时返回错误信息
        """
        # 设置默认配置
        default_config = {
            'default_rms_level': -90,  # 默认RMS电平 (dBm)
            'min_rms_level': -120,     # 最小RMS电平 (dBm)
            'quick_step': 5,           # 快速步进值 (dB)
            'slow_step': 1,            # 慢速步进值 (dB)
            'ref_per': 10              # 参考PER阈值 (%)
        }

        # 合并用户配置
        config = default_config.copy()
        if test_config:
            config.update(test_config)

        # 验证配置参数
        is_valid, error_msg = self._validate_sensitivity_test_config(config)
        if not is_valid:
            return False, f"配置参数验证失败: {error_msg}"

        # 提取配置参数
        default_rms_level = config['default_rms_level']
        min_rms_level = config['min_rms_level']
        max_rms_level = default_rms_level + 20
        quick_step = config['quick_step']
        slow_step = config['slow_step']
        ref_per = config['ref_per']
        quick_test_ref_per = ref_per // 2

        # 记录测试过程中的电平变化
        quick_rms_level_history = []
        slow_rms_level_history = []
        result_history = []

        self.write_info_log("✨ 开始灵敏度测试数据解析")
        self.write_info_log(f"🔧 测试参数: default_rms_level={default_rms_level}dBm, quick_step={quick_step}dB, slow_step={slow_step}dB, ref_per={ref_per}%")

        try:
            # 步骤1：获取默认RMS电平下的初始PER值
            self.write_info_log(f"🔍 步骤1: 默认RMS电平 {default_rms_level}dBm时 并获取初始PER值")

            ret, result = self._perform_sensitivity_test_at_level(default_rms_level, params, reg_addr, h_bit, l_bit)
            if ret is False:
                return False, f"获取初始PER值失败: {result}"

            per = result["per"]
            quick_rms_level = default_rms_level
            quick_rms_level_history.append(quick_rms_level)
            result_history.append(result)

            self.write_info_log(f"📊 初始测试结果: RMS电平={quick_rms_level}dBm, PER={per}%")

            # 步骤2.1：快速步进阶段
            self.write_info_log("🚀 步骤2.1: 开始快速步进阶段")
            quick_phase_count = 0
            if per >= quick_test_ref_per:
                # 如果PER >= quick_test_ref_per，则增加电平直到PER < quick_test_ref_per
                while per >= quick_test_ref_per and quick_rms_level <= max_rms_level:
                    quick_phase_count += 1
                    quick_rms_level = quick_rms_level + quick_step

                    self.write_info_log(f"🔽 快速步进 {quick_phase_count}: 设置RMS电平为 {quick_rms_level}dBm")

                    ret, result = self._perform_sensitivity_test_at_level(quick_rms_level, params, reg_addr, h_bit, l_bit)
                    if ret is False:
                        return False, f"快速步进阶段测试失败: {result}"

                    per = result["per"]
                    quick_rms_level_history.append(quick_rms_level)
                    result_history.append(result)

                    self.write_info_log(f"📊 快速步进结果: RMS电平={quick_rms_level}dBm, PER={per}%")

                    if per < quick_test_ref_per:
                        self.write_info_log(f"✅ 快速步进阶段完成: PER={per}% < {quick_test_ref_per}%")
                        break

                if quick_rms_level > max_rms_level:
                    return False, f"快速步进阶段调节电平值大于等于 {max_rms_level}，测试终止"

                slow_rms_level = quick_rms_level
                slow_rms_level_history.append(slow_rms_level)
                result_history.append(result)
                self.write_info_log(f"📊 回调RMS电平测试结果: RMS电平={slow_rms_level}dBm, PER={per}%")

            else:   # per < quick_test_ref_per:
                # 如果PER < quick_test_ref_per，则降低电平直到PER >= quick_test_ref_per
                while per < quick_test_ref_per and quick_rms_level > min_rms_level:
                    quick_phase_count += 1
                    quick_rms_level = quick_rms_level - quick_step

                    self.write_info_log(f"🔽 快速步进 {quick_phase_count}: 设置RMS电平为 {quick_rms_level}dBm")

                    ret, result = self._perform_sensitivity_test_at_level(quick_rms_level, params, reg_addr, h_bit, l_bit)
                    if ret is False:
                        return False, f"快速步进阶段测试失败: {result}"

                    per = result["per"]
                    quick_rms_level_history.append(quick_rms_level)
                    result_history.append(result)

                    self.write_info_log(f"📊 快速步进结果: RMS电平={quick_rms_level}dBm, PER={per}%")

                    if per >= quick_test_ref_per:
                        self.write_info_log(f"✅ 快速步进阶段完成: PER={per}% >= {quick_test_ref_per}%")
                        break

                if quick_rms_level <= min_rms_level:
                    return False, f"快速步进阶段调节电平值小于等于 {min_rms_level}，测试终止"

                # 步骤2.2：回退到上一个电平，然后获取PER值
                # 优化1：增加数组越界检查
                if len(quick_rms_level_history) < 2:
                    return False, "快速步进阶段数据不足，无法进行慢速步进"

                slow_start_rms_level = quick_rms_level_history[-2]
                self.write_info_log(f"🔍 步骤2.2: 回退RMS电平 {slow_start_rms_level}dBm时 并获取PER值")

                ret, result = self._perform_sensitivity_test_at_level(slow_start_rms_level, params, reg_addr, h_bit, l_bit)
                if ret is False:
                    return False, f"获取回退电平PER值失败: {result}"

                per = result["per"]
                slow_rms_level = slow_start_rms_level
                slow_rms_level_history.append(slow_rms_level)
                result_history.append(result)

                self.write_info_log(f"📊 回退RMS电平测试结果: RMS电平={slow_rms_level}dBm, PER={per}%")

            # 步骤2.3：慢速步进阶段 - 从当前电平开始，以slow_step递减直到PER >= ref_per
            self.write_info_log("🐌 步骤2.3: 开始慢速步进阶段")
            slow_phase_count = 0
            sensitivity_rms_level = None
            sensitivity_result_full_data = dict()
            sensitivity_per = None
            sensitivity_polar_err = None

            while per < ref_per and slow_rms_level > min_rms_level:
                slow_phase_count += 1
                # 记录上一次的电平作为潜在的灵敏度电平
                slow_rms_level = slow_rms_level - slow_step

                self.write_info_log(f"🔽 慢速步进 {slow_phase_count}: 设置RMS电平为 {slow_rms_level}dBm")

                ret, result = self._perform_sensitivity_test_at_level(slow_rms_level, params, reg_addr, h_bit, l_bit)
                if ret is False:
                    return False, f"慢速步进阶段测试失败: {result}"

                per = result["per"]
                slow_rms_level_history.append(slow_rms_level)
                result_history.append(result)

                self.write_info_log(f"📊 慢速步进结果: RMS电平={slow_rms_level}dBm, PER={per}%")

                if per >= ref_per:
                    # 步骤2.4：记录上次的电平为灵敏度电平
                    # 优化1：增加数组越界检查
                    if len(slow_rms_level_history) < 2 or len(result_history) < 2:
                        return False, "慢速步进阶段数据不足，无法确定灵敏度电平"

                    sensitivity_rms_level = slow_rms_level_history[-2]
                    sensitivity_result_full_data = result_history[-2]
                    sensitivity_per = result_history[-2]["per"]
                    sensitivity_polar_err = result_history[-2]["polar_err"]

                    self.write_info_log(f"✅ 慢速步进阶段完成: PER={per}% >= {ref_per}%")
                    self.write_info_log(f"🎯 灵敏度电平确定: {sensitivity_rms_level}dBm (PER={sensitivity_per}%)")
                    break

            if slow_rms_level <= min_rms_level:
                return False, f"慢速步进阶段调节电平值小于等于 {min_rms_level}，测试终止"

            if sensitivity_rms_level is None:
                return False, "未能确定灵敏度电平，测试失败"

            # 构建返回结果
            sensitivity_result = {
                'sensitivity': sensitivity_rms_level,  # 灵敏度电平 (dBm)
                'sensitivity_result_full_data': sensitivity_result_full_data,  # 灵敏度电平对应的完整测试结果
                'per': sensitivity_per,                      # 灵敏度电平对应的PER值
                'polar_err': sensitivity_polar_err,          # 极化误差
            }

            self.write_info_log("✨ 灵敏度测试数据解析完成")
            self.write_info_log(f"🏆 最终结果: 灵敏度电平={sensitivity_rms_level}dBm, PER={sensitivity_per}%, 极化误差={sensitivity_polar_err}")

            return True, sensitivity_result

        except Exception as e:
            error_msg = f"灵敏度测试过程中发生异常: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg
        finally:
            # 优化4：确保在任何情况下都清理测试状态
            self._cleanup_test_state()

    def sagitta_dut_get_ci_test_result(self, params: str, ref_value: int or float):
        try:
            ret, error = self.uart_handle.interact_with_sagitta_dut_end_test()
            if not ret:
                self.write_error_log(f"Failed to ended rf board test: {error}")
                return False, error
            time.sleep(0.5)

            ret = self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
            if not ret:
                error = "Failed to configure test params"
                return False, error
            time.sleep(0.5)

            return self.sagitta_dut_parse_ci_test_read_data(
                params=params, reg_addr="0xb40211a8", h_bit=16, l_bit=0, ref_value=ref_value)
        except Exception as e:
            return False, str(e)

    def _validate_ci_test_config(self, test_config: dict) -> tuple:
        """
        验证C/I测试配置参数

        Args:
            test_config: 测试配置字典

        Returns:
            tuple: (is_valid, error_message)
        """
        required_keys = ['default_rms_level', 'max_rms_level', 'quick_step', 'slow_step', 'ref_per']

        for key in required_keys:
            if key not in test_config:
                return False, f"缺少必需的配置参数: {key}"

        # 验证参数范围
        if test_config['default_rms_level'] >= test_config['max_rms_level']:
            return False, "默认RMS电平必须小于最大RMS电平"

        if test_config['quick_step'] <= 0 or test_config['slow_step'] <= 0:
            return False, "步进值必须大于0"

        if test_config['ref_per'] <= 0 or test_config['ref_per'] > 100:
            return False, "参考PER阈值必须在0-100%之间"

        return True, None

    def sagitta_dut_parse_ci_test_read_data(
            self, params: str, reg_addr: str, h_bit: int = 16, l_bit: int = 0, test_config: dict = None,
            ref_value: int or float = None):
        """
        C/I测试数据解析函数（优化版本）

        Args:
            params: 测试参数字典，包含以下字段：
                - sym_rate: 符号率
                - signal_ch: 信道
                - s_code_en: 编码使能
                - scode_rate: 编码率
                - snr: 信噪比
                - data_len: 数据长度
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数
            test_config: 测试配置字典，包含以下可选字段：
                - default_rms_level: 默认RMS电平 (dBm)，默认-90
                - max_rms_level: 最小RMS电平 (dBm)，默认-120
                - quick_step: 快速步进值 (dB)，默认5
                - slow_step: 慢速步进值 (dB)，默认1
                - ref_per: 参考PER阈值 (%)，默认10

        Returns:
            tuple: (success, result)
                success: bool, 是否成功
                result: dict 或 str, 成功时返回结果字典，失败时返回错误信息
        """
        # 设置默认配置
        if ref_value is None:
            rms_level_ref = -90
        else:
            rms_level_ref = ref_value
        default_config = {
            'default_rms_level': rms_level_ref,     # 默认RMS电平 (dBm)
            'max_rms_level': 5,                     # 最大RMS电平 (dBm)
            'quick_step': 5,                        # 快速步进值 (dB)
            'slow_step': 1,                         # 慢速步进值 (dB)
            'ref_per': 10                           # 参考PER阈值 (%)
        }

        # 合并用户配置
        config = default_config.copy()
        if test_config:
            config.update(test_config)

        # 验证配置参数
        is_valid, error_msg = self._validate_ci_test_config(config)
        if not is_valid:
            return False, f"配置参数验证失败: {error_msg}"

        # 提取配置参数
        default_rms_level = config['default_rms_level']
        max_rms_level = config['max_rms_level']
        min_rms_level = default_rms_level - 20
        quick_step = config['quick_step']
        slow_step = config['slow_step']
        ref_per = config['ref_per']
        quick_test_ref_per = ref_per // 2

        # 记录测试过程中的电平变化
        quick_rms_level_history = []
        slow_rms_level_history = []
        result_history = []

        self.write_debug_log("✨ 开始C/I测试数据解析")
        self.write_debug_log(f"🔧 测试参数: default_rms_level={default_rms_level}dBm, quick_step={quick_step}dB, slow_step={slow_step}dB, ref_per={ref_per}%")

        try:
            # 步骤1：获取默认RMS电平下的初始PER值
            self.write_info_log(f"🔍 步骤1: 默认干扰信号RMS电平 {default_rms_level}dBm时 并获取初始PER值")

            ret, result = self._perform_ci_test_at_inf_level(default_rms_level, params, reg_addr, h_bit, l_bit)
            if ret is False:
                return False, f"获取初始PER值失败: {result}"

            per = result["per"]
            quick_rms_level = default_rms_level
            quick_rms_level_history.append(quick_rms_level)
            result_history.append(result)

            self.write_info_log(f"📊 初始测试结果: 干扰信号RMS电平={quick_rms_level}dBm, PER={per}%")

            # 步骤2.1：快速步进阶段
            self.write_info_log("🚀 步骤2.1: 开始快速步进阶段")
            quick_phase_count = 0
            if per >= quick_test_ref_per:
                # 如果PER >= quick_test_ref_per，则降低电平直到PER < quick_test_ref_per
                while per >= quick_test_ref_per and quick_rms_level >= min_rms_level:
                    quick_phase_count += 1
                    quick_rms_level = quick_rms_level - quick_step

                    self.write_info_log(f"🔽 快速步进 {quick_phase_count}: 设置干扰信号RMS电平为 {quick_rms_level}dBm")

                    ret, result = self._perform_ci_test_at_inf_level(quick_rms_level, params, reg_addr, h_bit, l_bit)
                    if ret is False:
                        return False, f"快速步进阶段测试失败: {result}"

                    per = result["per"]

                    self.write_info_log(f"📊 快速步进结果: 干扰信号RMS电平={quick_rms_level}dBm, PER={per}%")

                    if per < quick_test_ref_per:
                        self.write_info_log(f"✅ 快速步进阶段完成: PER={per}% >= {quick_test_ref_per}%")
                        break

                if quick_rms_level < min_rms_level:
                    return False, f"快速步进阶段调节电平值小于 {min_rms_level}，测试终止"

                slow_rms_level = quick_rms_level
                slow_rms_level_history.append(slow_rms_level)
                result_history.append(result)
                self.write_info_log(f"📊 回调干扰信号RMS电平测试结果: RMS电平={slow_rms_level}dBm, PER={per}%")

            else:   # per < quick_test_ref_per:
                # 如果PER < quick_test_ref_per，则增加电平直到PER >= quick_test_ref_per
                while per < quick_test_ref_per and quick_rms_level < max_rms_level:
                    quick_phase_count += 1
                    quick_rms_level = quick_rms_level + quick_step

                    self.write_info_log(f"🔽 快速步进 {quick_phase_count}: 设置干扰信号RMS电平为 {quick_rms_level}dBm")

                    ret, result = self._perform_ci_test_at_inf_level(quick_rms_level, params, reg_addr, h_bit, l_bit)
                    if ret is False:
                        return False, f"快速步进阶段测试失败: {result}"

                    per = result["per"]
                    quick_rms_level_history.append(quick_rms_level)
                    result_history.append(result)

                    self.write_info_log(f"📊 快速步进结果: 干扰信号RMS电平={quick_rms_level}dBm, PER={per}%")

                    if per >= quick_test_ref_per:
                        self.write_info_log(f"✅ 快速步进阶段完成: PER={per}% >= {quick_test_ref_per}%")
                        break

                if quick_rms_level >= max_rms_level:
                    return False, f"快速步进阶段调节电平值大于等于 {max_rms_level}，测试终止"

                # 步骤2.2：回退到上一个电平，然后获取PER值
                # 优化1：增加数组越界检查
                if len(quick_rms_level_history) < 2:
                    return False, "快速步进阶段数据不足，无法进行慢速步进"

                slow_start_rms_level = quick_rms_level_history[-2]
                self.write_info_log(f"🔍 步骤2.2: 回退干扰信号RMS电平 {slow_start_rms_level}dBm时 并获取PER值")

                ret, result = self._perform_ci_test_at_inf_level(slow_start_rms_level, params, reg_addr, h_bit, l_bit)
                if ret is False:
                    return False, f"获取干扰信号回退电平PER值失败: {result}"

                per = result["per"]
                slow_rms_level = slow_start_rms_level
                slow_rms_level_history.append(slow_rms_level)
                result_history.append(result)

                self.write_info_log(f"📊 回退干扰信号RMS电平测试结果: RMS电平={slow_rms_level}dBm, PER={per}%")

            # 步骤2.3：慢速步进阶段 - 从当前电平开始，以slow_step递增直到PER >= ref_per
            self.write_info_log("🐌 步骤2.3: 开始慢速步进阶段")
            slow_phase_count = 0
            ci_test_rms_level = None
            ci_test_result_full_data = dict()
            ci_test_per = None
            ci_test_polar_err = None

            while per < ref_per and slow_rms_level < max_rms_level:
                slow_phase_count += 1
                # 记录上一次的电平作为潜在的C/I电平
                slow_rms_level = slow_rms_level + slow_step

                self.write_info_log(f"🔽 慢速步进 {slow_phase_count}: 设置干扰信号RMS电平为 {slow_rms_level}dBm")

                ret, result = self._perform_ci_test_at_inf_level(slow_rms_level, params, reg_addr, h_bit, l_bit)
                if ret is False:
                    return False, f"慢速步进阶段测试失败: {result}"

                per = result["per"]
                slow_rms_level_history.append(slow_rms_level)
                result_history.append(result)

                self.write_info_log(f"📊 慢速步进结果: 干扰信号RMS电平={slow_rms_level}dBm, PER={per}%")

                if per >= ref_per:
                    # 步骤2.4：记录上次的电平为CI电平
                    # 优化1：增加数组越界检查
                    if len(slow_rms_level_history) < 2 or len(result_history) < 2:
                        return False, "慢速步进阶段数据不足，无法确定C/I电平"

                    ci_test_rms_level = slow_rms_level_history[-2]
                    ci_test_result_full_data = result_history[-2]
                    ci_test_per = result_history[-2]["per"]
                    ci_test_polar_err = result_history[-2]["polar_err"]

                    self.write_info_log(f"✅ 慢速步进阶段完成: PER={per}% >= {ref_per}%")
                    self.write_info_log(f"🎯 C/I电平确定: {ci_test_rms_level}dBm (PER={ci_test_per}%)")
                    break

            if slow_rms_level >= max_rms_level:
                return False, f"慢速步进阶段调节电平值大于等于 {max_rms_level}，测试终止"

            if ci_test_rms_level is None:
                return False, "未能确定C/I电平，测试失败"

            # 构建返回结果
            ci_test_result = {
                'ci': ci_test_rms_level,  # C/I电平 (dBm)
                'ci_test_result_full_data': ci_test_result_full_data,  # C/I电平对应的完整测试结果
                'per': ci_test_per,                      # C/I电平对应的PER值
                'polar_err': ci_test_polar_err,          # 极化误差
            }

            self.write_info_log("✨ C/I测试数据解析完成")
            self.write_info_log(f"🏆 最终结果: C/I电平={ci_test_rms_level}dBm, PER={ci_test_per}%, 极化误差={ci_test_polar_err}")

            return True, ci_test_result

        except Exception as e:
            error_msg = f"C/I测试过程中发生异常: {str(e)}"
            self.write_error_log(error_msg)
            return False, error_msg
        finally:
            # 优化4：确保在任何情况下都清理测试状态
            self._cleanup_test_state()

    def _perform_ci_test_at_inf_level(self, rms_level: float, params: str, reg_addr: str, h_bit: int, l_bit: int):
        """
        干扰信号在指定RMS电平下执行CI测试

        Args:
            rms_level: RMS电平 (dBm)
            params: 测试参数
            reg_addr: 寄存器地址
            h_bit: 寄存器高位数
            l_bit: 寄存器低位数

        Returns:
            tuple: (success, result)
        """
        try:
            self.vsg_api.vsg_device_set_rf_output_power(rms_level, unit='dBm')
            time.sleep(1)  # 等待设备稳定

            self.uart_handle.interact_with_sagitta_dut_configure_test_params(params)
            time.sleep(0.5)

            return self.sagitta_dut_get_per_and_polar_err_data(reg_addr, h_bit, l_bit)
        except Exception as e:
            return False, f"干扰信号在RMS电平{rms_level}dBm下测试失败: {str(e)}"
