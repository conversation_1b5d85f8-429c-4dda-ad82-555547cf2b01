# -*- coding: utf-8 -*-
"""
Excel报告增强版使用示例
演示如何使用增强版的write_sagitta_test_data_with_error_handling函数
来规避程序中断或卡顿导致的数据丢失问题
"""

from module_set.handle_report_class import HandleExcelReport
import time

def example_usage():
    """使用示例"""
    
    # 创建报告处理对象
    report_file = "test_reports/enhanced_test_report.xlsx"
    handle = HandleExcelReport(filename=report_file)
    
    print("=== Excel报告增强版使用示例 ===")
    
    # 示例1：正常的Gauss测试数据写入
    print("\n1. 正常Gauss测试数据写入:")
    test_params_gauss = {
        'sym_rate': 0,
        'signal_ch': 10,
        's_code_en': 1,
        'scode_rate': 4,
        'snr': 2,
        'data_len': 6,
        'test_fre': 2412.007
    }
    
    test_result_gauss = {
        'per': 0.05,
        'polar_err': '1.2e-03~3.4e-03',
        'first_data': {'rx_ok': 1000, 'crc_err': 5},
        'second_data': {'rx_ok': 2000, 'crc_err': 10}
    }
    
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='6byte_Gauss',
        test_func='Gauss',
        test_params=test_params_gauss,
        test_result=test_result_gauss
    )
    print(f"写入结果: {'成功' if success else '失败'}")
    
    # 示例2：失败的Gauss测试数据写入（带错误信息）
    print("\n2. 失败Gauss测试数据写入:")
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='6byte_Gauss',
        test_func='Gauss',
        test_params=test_params_gauss,
        test_result=None,
        error_msg="设备连接超时，无法获取测试数据"
    )
    print(f"写入结果: {'成功' if success else '失败'}")
    
    # 示例3：Sensitivity测试数据写入
    print("\n3. Sensitivity测试数据写入:")
    test_params_sens = {
        'sym_rate': 1,
        'signal_ch': 15,
        's_code_en': 0,
        'scode_rate': 3,
        'snr': 1,
        'data_len': 10,
        'test_fre': 2437.0
    }
    
    test_result_sens = {
        'sensitivity': -85.5,
        'per': 10.2,
        'polar_err': '2.1e-04~5.6e-04',
        'first_data': {'rx_ok': 800, 'crc_err': 80},
        'second_data': {'rx_ok': 1600, 'crc_err': 160}
    }
    
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='10byte_Sensitivity',
        test_func='Sensitivity',
        test_params=test_params_sens,
        test_result=test_result_sens
    )
    print(f"写入结果: {'成功' if success else '失败'}")
    
    # 示例4：演示重试缓存数据功能
    print("\n4. 重试缓存数据:")
    retry_count = handle.retry_cached_data()
    print(f"重试成功的数据条数: {retry_count}")
    
    # 示例5：清空缓存
    print("\n5. 清空缓存:")
    handle._clear_cache()

def simulate_error_scenarios():
    """模拟错误场景"""
    
    print("\n=== 错误场景模拟 ===")
    
    # 创建一个可能出错的报告文件路径
    report_file = "/invalid_path/test_report.xlsx"  # 无效路径
    handle = HandleExcelReport(filename=report_file)
    
    test_params = {
        'sym_rate': 0,
        'signal_ch': 10,
        's_code_en': 1,
        'scode_rate': 4,
        'snr': 2,
        'data_len': 6,
        'test_fre': 2412.007
    }
    
    test_result = {
        'per': 0.05,
        'polar_err': '1.2e-03~3.4e-03'
    }
    
    print("尝试写入到无效路径（将触发缓存机制）:")
    success = handle.write_sagitta_test_data_with_error_handling(
        sheet_name='Error_Test',
        test_func='Gauss',
        test_params=test_params,
        test_result=test_result,
        max_retries=2  # 减少重试次数以快速演示
    )
    
    print(f"写入结果: {'成功' if success else '失败（数据已缓存）'}")

def best_practices():
    """最佳实践建议"""
    
    print("\n=== 最佳实践建议 ===")
    print("""
    1. 文件路径选择：
       - 确保报告文件路径有写入权限
       - 避免使用网络驱动器或临时目录
       - 建议使用项目目录下的子文件夹
    
    2. 错误处理：
       - 总是检查write_sagitta_test_data_with_error_handling的返回值
       - 在测试完成后调用retry_cached_data()重试失败的数据
       - 定期清理备份文件夹以节省磁盘空间
    
    3. 性能优化：
       - 避免频繁的小批量写入，尽量批量处理
       - 在测试开始前确保有足够的磁盘空间
       - 关闭其他可能占用Excel文件的程序
    
    4. 数据安全：
       - 重要测试完成后手动创建额外备份
       - 定期检查backup文件夹中的备份文件
       - 在程序异常退出后，检查data_cache.txt文件
    
    5. 监控和调试：
       - 关注控制台输出的错误信息
       - 检查backup文件夹中的备份文件
       - 使用retry_cached_data()恢复未成功写入的数据
    """)

if __name__ == "__main__":
    # 运行示例
    example_usage()
    
    # 模拟错误场景（可选）
    # simulate_error_scenarios()
    
    # 显示最佳实践
    best_practices()
    
    print("\n=== 示例完成 ===")
