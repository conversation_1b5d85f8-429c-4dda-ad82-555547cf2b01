from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.test_params import inf_freq_dev_chg, sagitta_symbol_rate_chg, ci_useful_level_chg, ci_test_ref_val_chg
from module_set.base_bluetooth_test import BaseBluetoothTest


class CiTest(BaseBluetoothTest):
    """C/I测试用例"""

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data, event_engine)
        self.case_func = "CI"
        self.select_cmw = True
        self.select_vsg = True

        # cmw配置参数
        self.cmw_config = {
            "signal_path": "RF1O",
            "tx_path": "TX1",
            "baseband_mode": "ARB",
            "repetition_mode": "CONTinuous",
            "rms_level": -60,  # dBm
            "freq_offset": 0.007  # MHz
        }

        # C/I测试特定参数配置
        self.test_params_config = {
            "SYM_RATES": [0],                   # 符号率配置 [0, 1, 2]
            "S_CODE_EN": [1],                   # 编码使能配置 [0, 1, 2]
            "SCODE_RATES": [2, 3, 4, 5, 6, 7],  # 编码率配置
            "SNRS": [100],                      # 信噪比配置
            "DATA_LEN": [6],                    # 数据长度配置
            "INF_FRE_OFFSET_POINT": [0, 1, 2, 3, 4],  # 干扰信号频率偏移点
            "INF_FRE_CALC_OP": [1, -1],         # 干扰信号频率计算操作，1表示加频率偏移，-1表示减频率偏移
            "MAX_TEST_CASES": 60,                # 最大测试用例数
        }

    def run_single_test(self, tc, report_sheet_name):
        """执行单次测试"""
        self.write_info_log(f"\n开始执行测试用例: {tc}")

        # 计算测试频率、干扰频率、干扰信号频率偏移
        useful_fre = 2402 + tc["signal_ch"] + self.cmw_config["freq_offset"]
        inf_fre = useful_fre + tc["inf_fre_offset"]
        fsk_freq_dev = inf_freq_dev_chg[sagitta_symbol_rate_chg[tc["sym_rate"]]]

        # 获取测试参考值
        self.write_info_log("{}".format(sagitta_symbol_rate_chg[tc["sym_rate"]]))
        self.write_info_log("{}".format(ci_test_ref_val_chg[sagitta_symbol_rate_chg[tc["sym_rate"]]]))
        self.write_info_log("{}".format(tc["inf_fre_offset_point"]))
        cur_sr_ref_val_dict = ci_test_ref_val_chg[sagitta_symbol_rate_chg[tc["sym_rate"]]]
        ref_value = cur_sr_ref_val_dict[tc["inf_fre_offset_point"]]
        self.write_info_log("测试电平起始参考值: {}dBm".format(ref_value))

        # VSG配置参数
        self.vsg_config = {
            "fsk_freq_dev": fsk_freq_dev,  # Hz
            "gaussian_filter_bbt": 0.5,
            "sym_rate": sagitta_symbol_rate_chg[tc["sym_rate"]] * 1e6,  # sps
            "pn_sequence": "PN15",
            "default_rms_level": -60,  # dBm
        }

        test_params = {
            "sym_rate": tc["sym_rate"],
            "signal_ch": tc["signal_ch"],
            "s_code_en": tc["s_code_en"],
            "scode_rate": tc["scode_rate"],
            "snr": tc["snr"],
            "data_len": tc["data_len"],
            "useful_fre": useful_fre,
            "useful_level": ci_useful_level_chg[sagitta_symbol_rate_chg[tc["sym_rate"]]],
            "inf_fre_offset_str": '{}'.format("frx+{}M".format(tc["inf_fre_offset"]) if tc["inf_fre_calc_op"] == 1 else "frx{}M".format(tc["inf_fre_offset"])),
            "inf_fre": inf_fre,
            "inf_fre_offset": tc["inf_fre_offset"],
            "inf_fre_calc_op": tc["inf_fre_calc_op"],
        }

        # 配置CMW
        ret, error = self.setup_cmw_generator(useful_fre)
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 加载ARB文件
        ret, error = self.sagitta_test_load_arb_file(test_params)
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 启动cmw信号发生器
        self.cmw_api.cmw_device_set_gprf_generator_state("ON")
        ret, error = self.cmw_api.cmw_func_gprf_generator_turn_on_status_wait_load_arb_file()
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 配置干扰信号发生器
        ret, error = self.ci_test_setup_vsg_generator(inf_fre)
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 初始化设备（如果需要）
        if self.rf_board_init_state:
            self.write_info_log("初始化RF板...")
            self.uart_handle.interact_with_sagitta_dut_init_device()

        # 执行测试并获取结果
        cur_case_params = f"bb test_cmw {tc['signal_ch']} {tc['sym_rate']} {tc['s_code_en']} {tc['scode_rate']} {tc['data_len']}"
        self.write_info_log(f"发送测试命令: {cur_case_params}")

        ret, result = self.sagitta_dut_get_ci_test_result(cur_case_params, ref_value)
        if ret:
            self.write_info_log(f"测试成功 - C/I功率值: {result['ci']}, PER: {result['per']}%, 极性误差: {result['polar_err']}")
            test_result = {
                'first_data': result['ci_test_result_full_data']["first_data"],
                'second_data': result['ci_test_result_full_data']["second_data"],
                'ci': result['ci'],
                'per': result["per"],
                'polar_err': result["polar_err"]
            }
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                test_result=test_result
            )
        else:
            self.write_error_log(f"测试失败: {result}")
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=result
            )

    def edit_cur_testcase_test_process(self):
        """主测试流程"""
        self.write_info_log("=== 开始C/I测试 ===")

        traversal_testcases = self.create_ci_testcases(self.test_params_config)
        if not traversal_testcases:
            self.write_error_log("没有可执行的测试用例，测试终止")
            return

        total_cases = len(traversal_testcases)
        self.write_info_log(f"总测试用例数: {total_cases}")

        for idx, tc in enumerate(traversal_testcases, 1):
            self.write_info_log(f"\n=== 执行测试用例 {idx}/{total_cases} ===")
            self.rf_board_init_state = (idx == 1)
            self.cur_case = tc

            report_sheet_name = self.report_config["sheet_name_template"].format(
                data_len=tc["data_len"],
                case_func=self.case_func
            )

            try:
                self.run_single_test(tc, report_sheet_name)
            except Exception as e:
                error_msg = f"测试用例 {idx} 执行失败: {str(e)}"
                self.write_error_log(error_msg)
                self.handle_report.write_sagitta_test_data_with_error_handling(
                    sheet_name=report_sheet_name,
                    test_func=self.case_func,
                    test_params=tc,
                    error_msg=error_msg
                )

        self.write_info_log("=== C/I测试完成 ===")

    def run(self, test_case_file=None):
        """重写run方法，传递正确的测试用例文件路径"""
        return super().run(test_case_file=__file__)

# 为了保持向后兼容，保留原来的类名
CaseRun = CiTest
