
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.base_bluetooth_test import BaseBluetoothTest


class GaussTest(BaseBluetoothTest):
    """高斯测试用例"""

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data, event_engine)
        self.case_func = "Gauss"
        self.select_cmw = True

        # 设备配置
        self.cmw_config = {
            "signal_path": "RF1O",
            "tx_path": "TX1",
            "baseband_mode": "ARB",
            "repetition_mode": "CONTinuous",
            "rms_level": -60,  # dBm
            "freq_offset": 0.007  # MHz
        }

        # 高斯测试特定参数配置
        self.test_params_config = {
            "SYM_RATES": [0, 1, 2],                   # 符号率配置 [0, 1, 2]
            "S_CODE_EN": [1],                   # 编码使能配置 [0, 1, 2]
            "SCODE_RATES": [2, 3, 4, 5, 6, 7],  # 编码率配置
            "SNRS": [1, 2, 3, 4, 5, 6],         # 信噪比配置
            "DATA_LEN": [6, 37, 255],                    # 数据长度配置
            "MAX_TEST_CASES": 100000                # 最大测试用例数
        }

    def run_single_test(self, tc, report_sheet_name):
        """执行单次测试"""
        self.write_info_log(f"\n开始执行测试用例: {tc}")

        # 计算测试频率
        test_fre = 2402 + tc["signal_ch"] + self.cmw_config["freq_offset"]
        test_params = {
            "sym_rate": tc["sym_rate"],
            "signal_ch": tc["signal_ch"],
            "s_code_en": tc["s_code_en"],
            "scode_rate": tc["scode_rate"],
            "snr": tc["snr"],
            "data_len": tc["data_len"],
            "test_fre": test_fre
        }

        # 配置CMW
        ret, error = self.setup_cmw_generator(test_fre)
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 加载ARB文件
        ret, error = self.sagitta_test_load_arb_file(test_params)
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 启动信号发生器
        self.cmw_api.cmw_device_set_gprf_generator_state("ON")
        ret, error = self.cmw_api.cmw_func_gprf_generator_turn_on_status_wait_load_arb_file()
        if not ret:
            self.write_error_log("{}".format(error))
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=error
            )

        # 初始化设备（如果需要）
        if self.rf_board_init_state:
            self.write_info_log("初始化RF板...")
            self.uart_handle.interact_with_sagitta_dut_init_device()

        # 执行测试并获取结果
        cur_case_params = f"bb test_cmw {tc['signal_ch']} {tc['sym_rate']} {tc['s_code_en']} {tc['scode_rate']} {tc['data_len']}"
        self.write_info_log(f"发送测试命令: {cur_case_params}")

        ret, result = self.sagitta_dut_get_gauss_result(cur_case_params)
        if ret:
            self.write_info_log(f"测试成功 - PER: {result['per']}%, 极性误差: {result['polar_err']}")
            test_result = {
                'first_data': result["first_data"],
                'second_data': result["second_data"],
                'per': result["per"],
                'polar_err': result["polar_err"]
            }
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                test_result=test_result
            )
        else:
            self.write_error_log(f"测试失败: {result}")
            return self.handle_report.write_sagitta_test_data_with_error_handling(
                sheet_name=report_sheet_name,
                test_func=self.case_func,
                test_params=test_params,
                error_msg=result
            )

    def edit_cur_testcase_test_process(self):
        """主测试流程"""
        self.write_info_log("=== 开始高斯测试 ===")

        traversal_testcases = self.create_gauss_testcases(self.test_params_config)
        if not traversal_testcases:
            self.write_error_log("没有可执行的测试用例，测试终止")
            return

        total_cases = len(traversal_testcases)
        self.write_info_log(f"总测试用例数: {total_cases}")

        for idx, tc in enumerate(traversal_testcases, 1):
            self.write_info_log(f"\n=== 执行测试用例 {idx}/{total_cases} ===")
            self.rf_board_init_state = (idx == 1)
            self.cur_case = tc

            report_sheet_name = self.report_config["sheet_name_template"].format(
                data_len=tc["data_len"],
                case_func=self.case_func
            )

            try:
                self.run_single_test(tc, report_sheet_name)
            except Exception as e:
                error_msg = f"测试用例 {idx} 执行失败: {str(e)}"
                self.write_error_log(error_msg)
                self.handle_report.write_sagitta_test_data_with_error_handling(
                    sheet_name=report_sheet_name,
                    test_func=self.case_func,
                    test_params=tc,
                    error_msg=error_msg
                )

        self.write_info_log("=== 高斯测试完成 ===")

    def run(self, test_case_file=None):
        """重写run方法，传递正确的测试用例文件路径"""
        return super().run(test_case_file=__file__)

# 为了保持向后兼容，保留原来的类名
CaseRun = GaussTest
